<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/jwt_helper.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

try {
    // Usar el mismo método que generate_prompt_g.php
    $user = JWTHelper::requireAuth();
    $supabaseUserId = $user['user_id'];

    error_log('🔍 DEBUG get_user_credits - Usuario autenticado: ' . json_encode($user));
    error_log('🔍 DEBUG get_user_credits - User ID: ' . $supabaseUserId);

    // Obtener información del usuario
    $userStmt = $pdo->prepare("SELECT id, credits FROM users WHERE supabase_user_id = ?");
    $userStmt->execute([$supabaseUserId]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    // DEBUG: Ver qué devuelve la consulta
    error_log('🔍 DEBUG - Usuario encontrado: ' . json_encode($user));
    error_log('🔍 DEBUG - Buscando supabase_user_id: ' . $supabaseUserId);

    if (!$user) {
        // DEBUG: Verificar qué usuarios existen
        $allUsersStmt = $pdo->prepare("SELECT supabase_user_id, email FROM users LIMIT 5");
        $allUsersStmt->execute();
        $allUsers = $allUsersStmt->fetchAll(PDO::FETCH_ASSOC);
        error_log('🔍 DEBUG - Usuarios en DB: ' . json_encode($allUsers));

        http_response_code(404);
        echo json_encode([
            'error' => 'Usuario no encontrado',
            'debug' => 'No se encontró usuario con supabase_user_id: ' . $supabaseUserId,
            'users_in_db' => count($allUsers)
        ]);
        exit;
    }

    $internalUserId = $user['id'];
    $currentCredits = (int)$user['credits']; // SIMPLE: Solo usar users.credits

    echo json_encode([
        'success' => true,
        'credits' => $currentCredits
    ]);

} catch (Exception $e) {
    error_log('Error in get_user_credits.php: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Error interno del servidor',
        'details' => $e->getMessage()
    ]);
}
