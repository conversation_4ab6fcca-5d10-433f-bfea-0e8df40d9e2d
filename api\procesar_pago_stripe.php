<?php
/**
 * ENDPOINT INTERNO SEGURO PARA PROCESAMIENTO DE PAGOS STRIPE
 * 
 * Este archivo NO debe ser accesible directamente desde el navegador.
 * Solo acepta peticiones internas con token de seguridad.
 * 
 * Responsabilidades:
 * - Verificar seguridad (IP + token)
 * - Consultar sesión de Stripe
 * - Acreditar créditos con idempotencia
 * - Registrar transacciones
 */

// Cargar configuración
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/stripe_helper.php';

// Cargar variables de entorno
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Headers de seguridad
header('Content-Type: application/json');
header('X-Robots-Tag: noindex, nofollow');

// LOG DE SEGURIDAD: Registrar TODAS las peticiones
error_log('🔒 PROCESAR_PAGO - Petición recibida desde: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
error_log('🔒 PROCESAR_PAGO - User Agent: ' . ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown'));
error_log('🔒 PROCESAR_PAGO - Method: ' . $_SERVER['REQUEST_METHOD']);

try {
    // SEGURIDAD 1: Solo métodos POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Método no permitido']);
        exit;
    }

    // SEGURIDAD 2: Solo peticiones desde localhost
    $allowedIPs = ['127.0.0.1', '::1', 'localhost'];
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    if (!in_array($clientIP, $allowedIPs)) {
        error_log('🚨 PROCESAR_PAGO - Acceso denegado desde IP: ' . $clientIP);
        http_response_code(403);
        echo json_encode(['error' => 'Acceso denegado']);
        exit;
    }

    // SEGURIDAD 3: Verificar token secreto
    $providedToken = $_POST['internal_token'] ?? '';
    $expectedToken = $_ENV['INTERNAL_PROCESSING_TOKEN'] ?? '';
    
    if (empty($expectedToken) || $providedToken !== $expectedToken) {
        error_log('🚨 PROCESAR_PAGO - Token inválido');
        http_response_code(401);
        echo json_encode(['error' => 'Token de autorización inválido']);
        exit;
    }

    // SEGURIDAD 4: Verificar session_id
    $sessionId = $_POST['session_id'] ?? '';
    if (empty($sessionId)) {
        error_log('🚨 PROCESAR_PAGO - Session ID faltante');
        http_response_code(400);
        echo json_encode(['error' => 'Session ID requerido']);
        exit;
    }

    error_log('✅ PROCESAR_PAGO - Verificaciones de seguridad pasadas');
    error_log('🔍 PROCESAR_PAGO - Procesando session: ' . $sessionId);

    // PASO 1: Obtener información de la sesión de Stripe
    $result = StripeHelper::retrieveCheckoutSession($sessionId);
    
    if (!$result['success']) {
        error_log('❌ PROCESAR_PAGO - Error obteniendo sesión: ' . $result['error']);
        http_response_code(400);
        echo json_encode(['error' => 'Error obteniendo información de pago']);
        exit;
    }

    $sessionInfo = $result['session'];
    error_log('🔍 PROCESAR_PAGO - Sesión obtenida: ' . $sessionInfo->payment_status);

    // PASO 2: Verificar que el pago fue exitoso
    if ($sessionInfo->payment_status !== 'paid') {
        error_log('⚠️ PROCESAR_PAGO - Pago no confirmado: ' . $sessionInfo->payment_status);
        http_response_code(400);
        echo json_encode(['error' => 'Pago no confirmado']);
        exit;
    }

    // PASO 3: Acreditar créditos con idempotencia
    $creditsResult = addCreditsFromStripeSession($sessionInfo, $sessionId);
    
    if ($creditsResult['success']) {
        error_log('✅ PROCESAR_PAGO - Créditos acreditados exitosamente');
        echo json_encode([
            'success' => true,
            'message' => 'Créditos acreditados correctamente',
            'credits_added' => $creditsResult['credits_added'],
            'new_balance' => $creditsResult['new_balance']
        ]);
    } else {
        error_log('❌ PROCESAR_PAGO - Error acreditando créditos: ' . $creditsResult['error']);
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $creditsResult['error']
        ]);
    }

} catch (Exception $e) {
    error_log('💥 PROCESAR_PAGO - Exception: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Error interno del servidor']);
}

/**
 * Acreditar créditos desde sesión de Stripe con máxima idempotencia
 */
function addCreditsFromStripeSession($session, $sessionId) {
    global $pdo;
    
    try {
        // Obtener datos de la sesión
        $userId = $session->metadata->user_id ?? null;
        $credits = (int)($session->metadata->credits ?? 0);
        
        if (!$userId || !$credits) {
            return [
                'success' => false,
                'error' => 'Metadata de sesión inválida'
            ];
        }
        
        error_log('🔍 PROCESAR_PAGO - Datos extraídos:');
        error_log('  - User ID: ' . $userId);
        error_log('  - Credits: ' . $credits);
        
        $pdo->beginTransaction();
        
        // IDEMPOTENCIA: Verificar si ya se procesó esta transacción
        $checkStmt = $pdo->prepare("
            SELECT id, credits_amount FROM credit_transactions
            WHERE stripe_session_id = ? AND user_id = (SELECT id FROM users WHERE supabase_user_id = ?)
        ");
        $checkStmt->execute([$sessionId, $userId]);
        $existingTransaction = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingTransaction) {
            $pdo->rollback();
            error_log('⚠️ PROCESAR_PAGO - Transacción ya procesada: ' . $sessionId);
            return [
                'success' => true,
                'message' => 'Transacción ya procesada anteriormente',
                'credits_added' => $existingTransaction['credits_amount'],
                'duplicate' => true
            ];
        }
        
        // Obtener usuario
        $userStmt = $pdo->prepare("SELECT id, credits FROM users WHERE supabase_user_id = ?");
        $userStmt->execute([$userId]);
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            $pdo->rollback();
            return [
                'success' => false,
                'error' => 'Usuario no encontrado'
            ];
        }
        
        $internalUserId = $user['id'];
        $currentCredits = (int)$user['credits'];
        $newCredits = $currentCredits + $credits;
        
        // Actualizar créditos del usuario
        $updateStmt = $pdo->prepare("
            UPDATE users
            SET credits = ?, total_credits_purchased = total_credits_purchased + ?
            WHERE id = ?
        ");
        $updateStmt->execute([$newCredits, $credits, $internalUserId]);
        
        // Registrar la transacción
        $transactionStmt = $pdo->prepare("
            INSERT INTO credit_transactions
            (user_id, transaction_type, credits_amount, credits_before, credits_after, description, stripe_session_id, created_at)
            VALUES (?, 'purchase', ?, ?, ?, ?, ?, NOW())
        ");
        
        $transactionStmt->execute([
            $internalUserId,
            $credits,
            $currentCredits,
            $newCredits,
            "Compra de $credits créditos - Stripe (Procesamiento Seguro)",
            $sessionId
        ]);
        
        $pdo->commit();
        
        error_log('✅ PROCESAR_PAGO - Transacción completada:');
        error_log('  - Créditos antes: ' . $currentCredits);
        error_log('  - Créditos después: ' . $newCredits);
        
        return [
            'success' => true,
            'credits_added' => $credits,
            'new_balance' => $newCredits,
            'previous_balance' => $currentCredits
        ];
        
    } catch (Exception $e) {
        $pdo->rollback();
        return [
            'success' => false,
            'error' => 'Error en base de datos: ' . $e->getMessage()
        ];
    }
}
?>
