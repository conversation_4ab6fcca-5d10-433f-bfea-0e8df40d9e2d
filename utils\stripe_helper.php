<?php
require_once __DIR__ . '/../vendor/autoload.php';

use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;

class StripeHelper {
    private static $initialized = false;
    
    /**
     * Inicializar Stripe con la clave secreta
     */
    public static function init() {
        if (!self::$initialized) {
            $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
            $dotenv->load();
            
            Stripe::setApiKey($_ENV['STRIPE_SECRET_KEY']);
            self::$initialized = true;
        }
    }
    
    /**
     * Crear una sesión de checkout para comprar créditos
     * 
     * @param int $credits Cantidad de créditos a comprar
     * @param float $amount Precio en USD (ej: 5.00 para $5)
     * @param string $userId ID del usuario de Supabase
     * @param string $successUrl URL de éxito
     * @param string $cancelUrl URL de cancelación
     * @return array Resultado con session_id o error
     */
    public static function createCheckoutSession($credits, $amount, $userId, $successUrl, $cancelUrl) {
        try {
            self::init();
            
            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => "FastlyAI Credits - {$credits} créditos",
                            'description' => "Compra {$credits} créditos para usar en FastlyAI",
                        ],
                        'unit_amount' => $amount * 100, // Stripe usa centavos
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => $successUrl,
                'cancel_url' => $cancelUrl,
                'metadata' => [
                    'user_id' => $userId,
                    'credits' => $credits,
                    'type' => 'credit_purchase'
                ],
                'customer_email' => null, // Se puede agregar el email del usuario si está disponible
            ]);
            
            return [
                'success' => true,
                'session_id' => $session->id,
                'checkout_url' => $session->url
            ];
            
        } catch (ApiErrorException $e) {
            error_log('Stripe API Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error al crear la sesión de pago: ' . $e->getMessage()
            ];
        } catch (Exception $e) {
            error_log('General Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error interno del servidor'
            ];
        }
    }
    
    /**
     * Verificar una sesión de checkout
     * 
     * @param string $sessionId ID de la sesión
     * @return array Información de la sesión
     */
    public static function retrieveCheckoutSession($sessionId) {
        try {
            self::init();
            
            $session = Session::retrieve($sessionId);
            
            return [
                'success' => true,
                'session' => $session,
                'payment_status' => $session->payment_status,
                'metadata' => $session->metadata->toArray()
            ];
            
        } catch (ApiErrorException $e) {
            error_log('Stripe API Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error al verificar la sesión: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Obtener la clave pública de Stripe para el frontend
     * 
     * @return string Clave pública
     */
    public static function getPublishableKey() {
        $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
        $dotenv->load();
        
        return $_ENV['STRIPE_PUBLISHABLE_KEY'];
    }
    
    /**
     * Validar webhook de Stripe
     * 
     * @param string $payload Payload del webhook
     * @param string $signature Firma del webhook
     * @param string $endpointSecret Secreto del endpoint
     * @return array Evento validado o error
     */
    public static function validateWebhook($payload, $signature, $endpointSecret) {
        try {
            self::init();
            
            $event = \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                $endpointSecret
            );
            
            return [
                'success' => true,
                'event' => $event
            ];
            
        } catch (\UnexpectedValueException $e) {
            return [
                'success' => false,
                'error' => 'Payload inválido'
            ];
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            return [
                'success' => false,
                'error' => 'Firma inválida'
            ];
        }
    }
}
