/**
 * JavaScript para la página de pricing
 */

// Variables globales
let currentUser = null;

/**
 * Inicializar la página de pricing
 */
document.addEventListener('DOMContentLoaded', function() {
    initializePricing();
});

/**
 * Inicializar funcionalidades de pricing
 */
async function initializePricing() {
    try {
        // Verificar si el usuario está autenticado
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
            currentUser = user;
            await loadUserCredits();
            showUserCreditsDisplay();
        }
        
    } catch (error) {
        console.error('Error al inicializar pricing:', error);
    }
}

/**
 * Cargar créditos del usuario
 */
async function loadUserCredits() {
    if (!currentUser) return;
    
    try {
        const response = await fetch('api/get_user_credits.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: currentUser.id
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            updateCreditsDisplay(data.credits);
        }
        
    } catch (error) {
        console.error('Error al cargar créditos:', error);
    }
}

/**
 * Mostrar el display de créditos del usuario
 */
function showUserCreditsDisplay() {
    const creditsDisplay = document.getElementById('user-credits-display');
    if (creditsDisplay) {
        creditsDisplay.classList.remove('hidden');
    }
}

/**
 * Actualizar el display de créditos
 */
function updateCreditsDisplay(credits) {
    const creditsCount = document.getElementById('user-credits-count');
    if (creditsCount) {
        creditsCount.textContent = credits.toLocaleString();
    }
}

/**
 * Función para comprar créditos
 */
async function purchaseCredits(planId, credits, price) {
    if (!currentUser) {
        // Redirigir al login si no está autenticado
        alert('Debes iniciar sesión para comprar créditos');
        return;
    }
    
    try {
        // Mostrar loading
        showPurchaseLoading(planId);
        
        // Aquí integrarías con Stripe o tu procesador de pagos
        // Por ahora, simularemos una compra exitosa para testing
        
        const response = await fetch('api/purchase_credits.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: currentUser.id,
                plan_id: planId,
                credits: credits,
                price: price,
                payment_method: 'test' // En producción sería el token de Stripe
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Actualizar créditos en la UI
            updateCreditsDisplay(data.new_credits);
            
            // Mostrar mensaje de éxito
            showSuccessMessage(`¡Compra exitosa! Se han agregado ${credits} créditos a tu cuenta.`);
            
        } else {
            throw new Error(data.message || 'Error en la compra');
        }
        
    } catch (error) {
        console.error('Error en la compra:', error);
        showErrorMessage('Error al procesar la compra. Por favor, inténtalo de nuevo.');
    } finally {
        hidePurchaseLoading(planId);
    }
}

/**
 * Mostrar loading en el botón de compra
 */
function showPurchaseLoading(planId) {
    const buttons = document.querySelectorAll(`button[onclick*="purchaseCredits(${planId}"]`);
    buttons.forEach(button => {
        button.disabled = true;
        button.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Procesando...
        `;
    });
}

/**
 * Ocultar loading en el botón de compra
 */
function hidePurchaseLoading(planId) {
    const buttons = document.querySelectorAll(`button[onclick*="purchaseCredits(${planId}"]`);
    buttons.forEach(button => {
        button.disabled = false;
        button.innerHTML = 'Comprar Créditos';
    });
}

/**
 * Mostrar mensaje de éxito
 */
function showSuccessMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            ${message}
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Remover después de 5 segundos
    setTimeout(() => {
        alertDiv.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(alertDiv);
        }, 300);
    }, 5000);
}

/**
 * Mostrar mensaje de error
 */
function showErrorMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300';
    alertDiv.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            ${message}
        </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Remover después de 5 segundos
    setTimeout(() => {
        alertDiv.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(alertDiv);
        }, 300);
    }, 5000);
}

/**
 * Escuchar cambios en el estado de autenticación
 */
supabase.auth.onAuthStateChange((event, session) => {
    if (event === 'SIGNED_IN' && session?.user) {
        currentUser = session.user;
        loadUserCredits();
        showUserCreditsDisplay();
    } else if (event === 'SIGNED_OUT') {
        currentUser = null;
        const creditsDisplay = document.getElementById('user-credits-display');
        if (creditsDisplay) {
            creditsDisplay.classList.add('hidden');
        }
    }
});
