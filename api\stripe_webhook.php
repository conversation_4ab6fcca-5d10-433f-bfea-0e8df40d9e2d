<?php
require_once __DIR__ . '/../utils/stripe_helper.php';
require_once __DIR__ . '/../config/database.php';

// DEBUG: Registrar TODAS las llamadas al webhook
error_log('🔥 WEBHOOK LLAMADO - ' . date('Y-m-d H:i:s'));
error_log('🔥 WEBHOOK METHOD: ' . $_SERVER['REQUEST_METHOD']);
error_log('🔥 WEBHOOK HEADERS: ' . json_encode(getallheaders()));
error_log('🔥 WEBHOOK BODY: ' . file_get_contents('php://input'));

// Configurar headers para webhook
header('Content-Type: application/json');

// Obtener el payload y la firma
$payload = @file_get_contents('php://input');
$signature = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';

// Secreto del endpoint (deberás configurarlo en Stripe Dashboard)
$endpointSecret = $_ENV['STRIPE_WEBHOOK_SECRET'] ?? '';

if (empty($endpointSecret)) {
    error_log('STRIPE_WEBHOOK_SECRET no configurado');
    http_response_code(400);
    echo json_encode(['error' => 'Webhook no configurado']);
    exit;
}

// Validar el webhook
$webhookResult = StripeHelper::validateWebhook($payload, $signature, $endpointSecret);

if (!$webhookResult['success']) {
    error_log('Webhook validation failed: ' . $webhookResult['error']);
    http_response_code(400);
    echo json_encode(['error' => $webhookResult['error']]);
    exit;
}

$event = $webhookResult['event'];

try {
    // Manejar diferentes tipos de eventos
    switch ($event->type) {
        case 'checkout.session.completed':
            handleCheckoutSessionCompleted($event->data->object);
            break;
            
        case 'payment_intent.succeeded':
            handlePaymentIntentSucceeded($event->data->object);
            break;
            
        default:
            error_log('Unhandled event type: ' . $event->type);
    }
    
    http_response_code(200);
    echo json_encode(['received' => true]);
    
} catch (Exception $e) {
    error_log('Webhook error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}

/**
 * Manejar checkout completado
 */
function handleCheckoutSessionCompleted($session) {
    error_log('🔍 WEBHOOK - Checkout session completed: ' . $session->id);
    error_log('🔍 WEBHOOK - Session data: ' . json_encode($session));

    // Obtener metadata
    $userId = $session->metadata->user_id ?? null;
    $credits = (int)($session->metadata->credits ?? 0);

    error_log('🔍 WEBHOOK - Extracted data:');
    error_log('  - User ID: ' . ($userId ?? 'NULL'));
    error_log('  - Credits: ' . $credits);
    error_log('  - Metadata: ' . json_encode($session->metadata));

    if (!$userId || !$credits) {
        error_log('🚨 WEBHOOK ERROR - Missing metadata in session');
        return;
    }
    
    // Agregar créditos al usuario
    addCreditsToUser($userId, $credits, $session->id);
}

/**
 * Manejar pago exitoso
 */
function handlePaymentIntentSucceeded($paymentIntent) {
    error_log('Payment intent succeeded: ' . $paymentIntent->id);
    // Aquí puedes agregar lógica adicional si es necesaria
}

/**
 * Agregar créditos al usuario en la base de datos
 */
function addCreditsToUser($supabaseUserId, $credits, $sessionId) {
    global $pdo;

    try {

        // Iniciar transacción
        $pdo->beginTransaction();

        // Obtener el ID interno del usuario usando supabase_user_id
        $userStmt = $pdo->prepare("SELECT id, credits FROM users WHERE supabase_user_id = ?");
        $userStmt->execute([$supabaseUserId]);
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            error_log("User not found with supabase_user_id: $supabaseUserId");
            $pdo->rollback();
            return;
        }

        $internalUserId = $user['id'];
        $currentCredits = $user['credits'];

        // Verificar si ya se procesó esta transacción
        $checkStmt = $pdo->prepare("
            SELECT id FROM credit_transactions
            WHERE stripe_session_id = ? AND user_id = ?
        ");
        $checkStmt->execute([$sessionId, $internalUserId]);

        if ($checkStmt->fetch()) {
            error_log("Transaction already processed: $sessionId");
            $pdo->rollback();
            return;
        }

        // Agregar créditos al usuario
        $newCredits = $currentCredits + $credits;

        error_log("🔍 WEBHOOK DEBUG - Agregando créditos:");
        error_log("  - Usuario ID: $internalUserId");
        error_log("  - Créditos actuales: $currentCredits");
        error_log("  - Créditos a agregar: $credits");
        error_log("  - Nuevos créditos: $newCredits");

        $updateStmt = $pdo->prepare("
            UPDATE users
            SET credits = ?, total_credits_purchased = total_credits_purchased + ?
            WHERE id = ?
        ");
        $updateStmt->execute([$newCredits, $credits, $internalUserId]);

        // Verificar que se actualizó correctamente
        $verifyStmt = $pdo->prepare("SELECT credits FROM users WHERE id = ?");
        $verifyStmt->execute([$internalUserId]);
        $verifyResult = $verifyStmt->fetch(PDO::FETCH_ASSOC);
        error_log("🔍 WEBHOOK DEBUG - Créditos después de UPDATE: " . ($verifyResult['credits'] ?? 'NULL'));

        // Registrar la transacción
        $transactionStmt = $pdo->prepare("
            INSERT INTO credit_transactions
            (user_id, transaction_type, credits_amount, credits_before, credits_after, description, stripe_session_id, created_at)
            VALUES (?, 'purchase', ?, ?, ?, ?, ?, NOW())
        ");
        $transactionStmt->execute([
            $internalUserId,
            $credits,
            $currentCredits,
            $newCredits,
            "Compra de $credits créditos via Stripe",
            $sessionId
        ]);

        // Confirmar transacción
        $pdo->commit();

        error_log("Successfully added $credits credits to user $supabaseUserId (internal ID: $internalUserId). Credits: $currentCredits -> $newCredits");

    } catch (Exception $e) {
        $pdo->rollback();
        error_log('Error adding credits: ' . $e->getMessage());
        throw $e;
    }
}
