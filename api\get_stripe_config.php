<?php
require_once __DIR__ . '/../utils/stripe_helper.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Si es una solicitud OPTIONS (preflight), terminar aquí
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Verificar que sea una solicitud GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

try {
    $publishableKey = StripeHelper::getPublishableKey();
    
    if (empty($publishableKey)) {
        throw new Exception('Clave pública de Stripe no configurada');
    }
    
    echo json_encode([
        'success' => true,
        'publishable_key' => $publishableKey
    ]);
    
} catch (Exception $e) {
    error_log('Error getting Stripe config: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error interno del servidor'
    ]);
}
