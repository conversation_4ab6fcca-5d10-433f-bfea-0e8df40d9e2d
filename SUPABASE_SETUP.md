# Configuración de Supabase

## Pasos para configurar Supabase

### 1. Crear un proyecto en Supabase
1. Ve a [supabase.com](https://supabase.com)
2. Crea una cuenta o inicia sesión
3. Crea un nuevo proyecto
4. Espera a que se complete la configuración

### 2. Configurar autenticación con Google
1. En el dashboard de Supabase, ve a **Authentication** > **Providers**
2. Busca **Google** y habilítalo
3. Necesitarás configurar OAuth con Google:
   - Ve a [Google Cloud Console](https://console.cloud.google.com)
   - Crea un nuevo proyecto o selecciona uno existente
   - Habilita la API de Google+
   - Ve a **Credentials** > **Create Credentials** > **OAuth 2.0 Client IDs**
   - Configura las URLs de redirección:
     - Para desarrollo: `https://[tu-proyecto].supabase.co/auth/v1/callback`
     - Para producción: `https://tu-dominio.com/auth/v1/callback`
   - Copia el **Client ID** y **Client Secret**
4. Vuelve a Supabase y pega las credenciales de Google

### 3. Obtener las credenciales de Supabase
1. En el dashboard de Supabase, ve a **Settings** > **API**
2. Copia los siguientes valores:
   - **URL**: Tu URL del proyecto
   - **anon public**: Tu clave pública anónima

### 4. Configurar la base de datos
1. En el dashboard de Supabase, ve a **SQL Editor**
2. Copia y pega el contenido del archivo `supabase_setup.sql`
3. Ejecuta el script para crear:
   - Tabla `profiles` para información adicional del usuario
   - Políticas de seguridad (RLS)
   - Triggers automáticos para crear perfiles

### 5. Configurar las credenciales en el proyecto
1. Abre el archivo `include/supabase_config.php`
2. Reemplaza los valores placeholder:
   ```php
   // Reemplaza estos valores con los de tu proyecto Supabase
   define('SUPABASE_URL', 'https://tu-proyecto.supabase.co');
   define('SUPABASE_ANON_KEY', 'tu-clave-publica-anonima');
   ```

### 6. Configurar las URLs de redirección
En tu proyecto de Supabase, ve a **Authentication** > **URL Configuration** y añade:
- **Site URL**: `http://localhost:8000` (para desarrollo)
- **Redirect URLs**: 
  - `http://localhost:8000` (para desarrollo)
  - `https://tu-dominio.com` (para producción)

## Funcionalidades implementadas

- ✅ Autenticación con Google OAuth
- ✅ Tabla de perfiles personalizada con triggers automáticos
- ✅ Detección automática del estado de autenticación
- ✅ Creación automática de perfiles de usuario
- ✅ Interfaz de usuario que se actualiza según el estado de login
- ✅ Cierre de sesión
- ✅ Persistencia de sesión entre recargas de página
- ✅ Logging detallado para debugging

## Notas importantes

1. **Seguridad**: La clave anónima es segura para usar en el frontend
2. **HTTPS**: En producción, asegúrate de usar HTTPS
3. **Dominios**: Configura correctamente los dominios permitidos en Supabase
4. **Políticas RLS**: Considera implementar Row Level Security si planeas almacenar datos de usuario

## Solución de problemas

### Error: "Invalid login credentials"
- Verifica que las credenciales de Google estén correctamente configuradas
- Asegúrate de que las URLs de redirección coincidan exactamente

### Error: "Not allowed by CORS policy"
- Verifica que tu dominio esté en la lista de URLs permitidas en Supabase

### El usuario no se autentica
- Verifica que las credenciales de Supabase sean correctas
- Revisa la consola del navegador para errores específicos
