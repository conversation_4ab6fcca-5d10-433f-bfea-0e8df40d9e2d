<?php
require_once 'database_config.php';

/**
 * Clase para manejar usuarios y créditos
 */
class UserManager {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Crear o actualizar usuario desde Supabase
     */
    public function createOrUpdateUser($supabaseUserId, $email, $fullName = null, $avatarUrl = null) {
        try {
            // Verificar si el usuario ya existe
            $stmt = $this->db->query(
                "SELECT id, credits FROM users WHERE supabase_user_id = ?",
                [$supabaseUserId]
            );
            
            $user = $stmt->fetch();
            
            if ($user) {
                // Actualizar usuario existente
                $this->db->query(
                    "UPDATE users SET email = ?, full_name = ?, avatar_url = ?, updated_at = NOW() WHERE supabase_user_id = ?",
                    [$email, $fullName, $avatarUrl, $supabaseUserId]
                );
                return $user['id'];
            } else {
                // Crear nuevo usuario con créditos de bienvenida
                $welcomeCredits = 10; // 10 créditos gratis para nuevos usuarios
                
                $this->db->query(
                    "INSERT INTO users (supabase_user_id, email, full_name, avatar_url, credits) VALUES (?, ?, ?, ?, ?)",
                    [$supabaseUserId, $email, $fullName, $avatarUrl, $welcomeCredits]
                );
                
                $userId = $this->db->lastInsertId();
                
                // Registrar transacción de créditos de bienvenida
                $this->addCreditTransaction($userId, 'bonus', $welcomeCredits, 0, $welcomeCredits, 'Créditos de bienvenida');
                
                return $userId;
            }
        } catch (Exception $e) {
            error_log("Error al crear/actualizar usuario: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Obtener información del usuario por ID de Supabase
     */
    public function getUserBySupabaseId($supabaseUserId) {
        try {
            $stmt = $this->db->query(
                "SELECT * FROM users WHERE supabase_user_id = ?",
                [$supabaseUserId]
            );
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error al obtener usuario: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Obtener créditos del usuario
     */
    public function getUserCredits($supabaseUserId) {
        try {
            $stmt = $this->db->query(
                "SELECT credits FROM users WHERE supabase_user_id = ?",
                [$supabaseUserId]
            );
            $result = $stmt->fetch();
            return $result ? $result['credits'] : 0;
        } catch (Exception $e) {
            error_log("Error al obtener créditos: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Usar créditos (restar)
     */
    public function useCredits($supabaseUserId, $creditsToUse, $description = 'Uso de prompt') {
        try {
            $this->db->beginTransaction();
            
            // Obtener créditos actuales
            $stmt = $this->db->query(
                "SELECT id, credits FROM users WHERE supabase_user_id = ? FOR UPDATE",
                [$supabaseUserId]
            );
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception("Usuario no encontrado");
            }
            
            if ($user['credits'] < $creditsToUse) {
                throw new Exception("Créditos insuficientes");
            }
            
            $newCredits = $user['credits'] - $creditsToUse;
            
            // Actualizar créditos
            $this->db->query(
                "UPDATE users SET credits = ?, updated_at = NOW() WHERE id = ?",
                [$newCredits, $user['id']]
            );
            
            // Registrar transacción
            $this->addCreditTransaction($user['id'], 'usage', -$creditsToUse, $user['credits'], $newCredits, $description);
            
            $this->db->commit();
            return $newCredits;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error al usar créditos: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Agregar créditos (compra)
     */
    public function addCredits($supabaseUserId, $creditsToAdd, $description = 'Compra de créditos', $referenceId = null) {
        try {
            $this->db->beginTransaction();
            
            // Obtener créditos actuales
            $stmt = $this->db->query(
                "SELECT id, credits FROM users WHERE supabase_user_id = ? FOR UPDATE",
                [$supabaseUserId]
            );
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception("Usuario no encontrado");
            }
            
            $newCredits = $user['credits'] + $creditsToAdd;
            
            // Actualizar créditos y total comprado
            $this->db->query(
                "UPDATE users SET credits = ?, total_credits_purchased = total_credits_purchased + ?, updated_at = NOW() WHERE id = ?",
                [$newCredits, $creditsToAdd, $user['id']]
            );
            
            // Registrar transacción
            $this->addCreditTransaction($user['id'], 'purchase', $creditsToAdd, $user['credits'], $newCredits, $description, $referenceId);
            
            $this->db->commit();
            return $newCredits;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Error al agregar créditos: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Registrar transacción de créditos
     */
    private function addCreditTransaction($userId, $type, $amount, $creditsBefore, $creditsAfter, $description, $referenceId = null) {
        $this->db->query(
            "INSERT INTO credit_transactions (user_id, transaction_type, credits_amount, credits_before, credits_after, description, reference_id) VALUES (?, ?, ?, ?, ?, ?, ?)",
            [$userId, $type, $amount, $creditsBefore, $creditsAfter, $description, $referenceId]
        );
    }
    
    /**
     * Obtener historial de transacciones del usuario
     */
    public function getUserTransactions($supabaseUserId, $limit = 50) {
        try {
            $stmt = $this->db->query(
                "SELECT ct.* FROM credit_transactions ct 
                 JOIN users u ON ct.user_id = u.id 
                 WHERE u.supabase_user_id = ? 
                 ORDER BY ct.created_at DESC 
                 LIMIT ?",
                [$supabaseUserId, $limit]
            );
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error al obtener transacciones: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Obtener planes de precios
     */
    public function getPricingPlans() {
        try {
            $stmt = $this->db->query(
                "SELECT * FROM pricing_plans WHERE is_active = 1 ORDER BY price_usd ASC"
            );
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error al obtener planes: " . $e->getMessage());
            return [];
        }
    }
}
