<?php
/**
 * Rate Limiter para la API de generación de prompts
 * Limita el número de solicitudes por IP o por usuario autenticado en un período de tiempo
 */

class RateLimiter {
    private $dataFile;
    private $maxRequests;
    private $timeWindow; // en segundos

    public function __construct($maxRequests = 5, $timeWindow = 86400) { // Por defecto: 5 solicitudes por día
        $this->dataFile = __DIR__ . '/rate_limits.json';
        $this->maxRequests = $maxRequests;
        $this->timeWindow = $timeWindow;

        // Crear el archivo si no existe
        if (!file_exists($this->dataFile)) {
            file_put_contents($this->dataFile, json_encode([]));
        }
    }

    /**
     * Verificar límite de solicitudes
     *
     * @param string|null $userId ID del usuario autenticado (opcional)
     * @return array Resultado de la verificación del límite
     */
    public function checkLimit($userId = null) {
        // Usar user_id si está disponible, sino usar IP
        $identifier = $userId ? 'user_' . $userId : 'ip_' . $this->getClientIP();
        $data = $this->loadData();

        // Limpiar registros antiguos
        $this->cleanOldRecords($data);

        // Verificar si el identificador existe y tiene límites
        if (!isset($data[$identifier])) {
            $data[$identifier] = [
                'count' => 0,
                'requests' => [],
                'type' => $userId ? 'user' : 'ip',
                'identifier_value' => $userId ?: $this->getClientIP()
            ];
        }

        // Contar solicitudes dentro de la ventana de tiempo
        $currentTime = time();
        $recentRequests = 0;

        foreach ($data[$identifier]['requests'] as $timestamp) {
            if ($currentTime - $timestamp < $this->timeWindow) {
                $recentRequests++;
            }
        }

        // Verificar si se ha excedido el límite
        if ($recentRequests >= $this->maxRequests) {
            return [
                'allowed' => false,
                'remaining' => 0,
                'reset_after' => $this->getOldestRequest($data[$identifier]['requests']) + $this->timeWindow - $currentTime,
                'identifier' => $identifier,
                'type' => $data[$identifier]['type']
            ];
        }

        // Registrar nueva solicitud
        $data[$identifier]['count']++;
        $data[$identifier]['requests'][] = $currentTime;
        $this->saveData($data);

        return [
            'allowed' => true,
            'remaining' => $this->maxRequests - $recentRequests - 1,
            'reset_after' => $this->timeWindow,
            'identifier' => $identifier,
            'type' => $data[$identifier]['type']
        ];
    }
    
    private function getOldestRequest($requests) {
        if (empty($requests)) return time();
        sort($requests);
        return $requests[0];
    }
    
    private function cleanOldRecords(&$data) {
        $currentTime = time();
        foreach ($data as &$info) {
            $info['requests'] = array_filter($info['requests'], function($timestamp) use ($currentTime) {
                return $currentTime - $timestamp < $this->timeWindow;
            });
        }
    }
    
    private function loadData() {
        $content = file_get_contents($this->dataFile);
        return json_decode($content, true) ?: [];
    }
    
    private function saveData($data) {
        file_put_contents($this->dataFile, json_encode($data));
    }
    
    private function getClientIP() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }
}