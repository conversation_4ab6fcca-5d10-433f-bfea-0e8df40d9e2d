<?php
// Incluir archivos necesarios
require_once './include/language.php';
?>
<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Políticas - Generador de Prompts IA</title>

    <!-- ⚡ Script INLINE para evitar flash del modo oscuro -->
    <script>
        (function() {
            if (localStorage.getItem('darkMode') === 'enabled' ||
                (localStorage.getItem('darkMode') === null && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                document.documentElement.classList.add('dark');
            }
        })();
    </script>

    <!-- Script para aplicar el modo oscuro inmediatamente -->
    <script type="module" src="./js/colorMode.js"></script>
    <link rel="stylesheet" href="./css/styles.css">
    <link rel="stylesheet" href="./css/custom.css">

    <?php  require './include/analytics.php'; ?>
    <script async src="https://tally.so/widgets/embed.js"></script>

    <!-- Scripts de Supabase para autenticación -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
    <script src="./js/auth.js"></script>
    <script src="./js/language.js"></script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- Navegación -->
    <nav class="bg-white shadow-sm sticky top-0 z-10 dark:bg-gray-800">

    <?php  require './include/nav.php';       ?>

        <!-- Menú móvil desplegable -->
        <?php require './include/nav_movil.php';?>
    </nav>

    <div class="main-content-wrapper">
        <div class="container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto content">
            <h1 class="text-4xl font-bold text-center mb-10 dark:text-white"><?php echo t('policies_title'); ?></h1>

            <div class="bg-white rounded-xl shadow-md p-8 mb-10 dark:bg-gray-800">
                <h2 class="text-2xl font-bold mb-4 dark:text-white"><?php echo t('data_collection_title'); ?></h2>
                <p class="text-gray-600 mb-6 dark:text-gray-300">
                    <?php echo t('data_collection_text'); ?>
                </p>

                <h2 class="text-2xl font-bold mb-4 dark:text-white"><?php echo t('ai_usage_title'); ?></h2>
                <p class="text-gray-600 mb-6 dark:text-gray-300">
                    <?php echo t('ai_usage_text'); ?>
                </p>

                <h2 class="text-2xl font-bold mb-4 dark:text-white"><?php echo t('cookies_title'); ?></h2>
                <p class="text-gray-600 mb-6 dark:text-gray-300">
                    <?php echo t('cookies_text'); ?>
                </p>

                <h2 class="text-2xl font-bold mb-4 dark:text-white"><?php echo t('policy_changes_title'); ?></h2>
                <p class="text-gray-600 mb-6 dark:text-gray-300">
                    <?php echo t('policy_changes_text'); ?>
                </p>
            </div>

            <div class="bg-white rounded-xl shadow-md p-8 dark:bg-gray-800">
                <h2 class="text-2xl font-bold mb-4 dark:text-white"><?php echo t('terms_title'); ?></h2>

                <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('responsible_use_title'); ?></h3>
                <p class="text-gray-600 mb-6 dark:text-gray-300">
                    <?php echo t('responsible_use_text'); ?>
                </p>

                <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('service_limitations_title'); ?></h3>
                <p class="text-gray-600 mb-6 dark:text-gray-300">
                    <?php echo t('service_limitations_text'); ?>
                </p>

                <h3 class="text-xl font-semibold mb-2 dark:text-white"><?php echo t('intellectual_property_title'); ?></h3>
                <p class="text-gray-600 mb-6 dark:text-gray-300">
                    <?php echo t('intellectual_property_text'); ?>
                </p>
            </div>
        </div>
    </div>
    </div>

    <!-- Footer -->

<?php  require './include/footer.php'; ?>



    <script>
        // Pasar las traducciones del modo oscuro al JavaScript
        window.translations = {
            dark_mode_text: <?php echo json_encode(t('dark_mode_text')); ?>,
            light_mode_text: <?php echo json_encode(t('light_mode_text')); ?>
        };

        // Funciones globales para la autenticación (compatibilidad con nav.php)
        function showLoginModal() {
            if (window.authSystem) {
                window.authSystem.showLoginModal();
            }
        }

        function showRegisterModal() {
            if (window.authSystem) {
                window.authSystem.showRegisterModal();
            }
        }

        function logout() {
            if (window.authSystem) {
                window.authSystem.logout();
            }
        }
    </script>
 <script src="./js/movilMenu.js"></script>
</body>
</html>