<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../include/user_manager.php';

try {
    // Verificar método
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }
    
    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validar datos requeridos
    $requiredFields = ['user_id', 'email'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field])) {
            throw new Exception("Campo requerido: $field");
        }
    }
    
    $userId = $input['user_id'];
    $email = $input['email'];
    $fullName = $input['full_name'] ?? null;
    $avatarUrl = $input['avatar_url'] ?? null;
    
    // Crear instancia del UserManager
    $userManager = new UserManager();
    
    // Crear o actualizar usuario
    $internalUserId = $userManager->createOrUpdateUser($userId, $email, $fullName, $avatarUrl);
    
    // Obtener información completa del usuario
    $user = $userManager->getUserBySupabaseId($userId);
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Usuario sincronizado correctamente',
        'user' => [
            'id' => $user['id'],
            'supabase_user_id' => $user['supabase_user_id'],
            'email' => $user['email'],
            'full_name' => $user['full_name'],
            'credits' => $user['credits'],
            'total_credits_purchased' => $user['total_credits_purchased'],
            'created_at' => $user['created_at']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
