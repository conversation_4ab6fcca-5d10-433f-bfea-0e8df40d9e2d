@tailwind base;
@tailwind components;
@tailwind utilities;

/* Estilos base */
@layer base {
  body {
    @apply bg-gray-50 text-gray-800 min-h-screen;
  }

  h1 {
    @apply text-3xl font-bold md:text-4xl;
  }

  h2 {
    @apply text-2xl font-bold md:text-3xl;
  }

  h3 {
    @apply text-xl font-semibold;
  }

  a {
    @apply transition-colors;
  }

  /* Modo oscuro */
  .dark body {
    @apply bg-dark-bg text-dark-text-primary;
  }
}

/* Componentes reutilizables */
@layer components {
  /* Navegación */
  .nav-link {
    @apply text-gray-600 hover:text-primary-600 font-medium dark:text-dark-text-secondary dark:hover:text-primary-400;
  }

  /* Botones */
  .btn-primary {
    @apply bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors text-base font-medium shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors dark:bg-dark-surface dark:text-dark-text-primary dark:hover:bg-dark-border;
  }

  /* Toggle modo oscuro */
  .dark-mode-toggle {
    @apply bg-gray-200 text-gray-800 px-3 py-1 rounded-md text-sm font-medium hover:bg-gray-300 transition-colors dark:bg-dark-border dark:text-dark-text-primary dark:hover:bg-gray-600;
  }

  /* Cards */
  .card {
    @apply bg-white rounded-xl shadow-sm p-6 dark:bg-dark-surface dark:border dark:border-dark-border;
  }

  .option-card {
    @apply bg-white rounded-xl shadow-sm p-4 border border-gray-100 hover:border-primary-300 hover:shadow-md transition-all cursor-pointer dark:bg-dark-surface dark:border-dark-border dark:hover:border-primary-700;
  }

  /* Aseguramos que los textos sean visibles en modo oscuro */
  .option-card h3 {
    @apply dark:text-white;
  }

  .option-card p {
    @apply dark:text-gray-300;
  }

  /* Inputs */
  .input-large {
    @apply w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-dark-surface dark:border-dark-border dark:text-dark-text-primary;
  }

  /* Menú móvil */
  .mobile-menu {
    @apply bg-white shadow-lg rounded-b-lg w-full z-20 transition-all dark:bg-dark-surface dark:border-dark-border;
  }

  .mobile-menu-link {
    @apply block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50 dark:text-dark-text-secondary dark:hover:text-primary-400 dark:hover:bg-dark-border;
  }
}

/* Utilidades personalizadas */
@layer utilities {
  /* Animación typewriter */
  .typewriter {
    @apply inline-block overflow-hidden border-r-2 border-primary-500 whitespace-nowrap text-primary-500 dark:text-primary-400 dark:border-primary-400;
    animation:
      typing 3.5s steps(40, end),
      blink-caret .75s step-end infinite;
  }

  /* Animación para el menú móvil */
  .slide-down {
    animation: slideDown 0.3s ease forwards;
  }

  /* Pantalla de carga */
  .loading-overlay {
    @apply fixed inset-0 bg-gray-900 bg-opacity-80 flex flex-col items-center justify-center z-50 hidden;
  }

  .spinner {
    @apply w-12 h-12 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin;
  }
}

/* Animaciones */
@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: theme('colors.primary.500') }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Media queries */
@media (max-width: 640px) {
  .container {
    @apply px-4;
  }

  .slide-container {
    min-height: 550px;
  }

  .slide {
    @apply py-4 px-2;
  }

  .input-large {
    @apply text-base py-3;
  }

  .option-card {
    @apply p-4;
  }

  .back-button {
    @apply left-2.5 top-[70px] w-10 h-10 text-lg;
  }
}
