/**
 * Funciones para manejar el cambio de idioma
 */

// Función para cambiar idioma sin query parameters
async function changeLanguage(lang) {
    try {
        console.log('🌐 Cambiando idioma a:', lang);

        // Guardar en localStorage
        localStorage.setItem('user_language', lang);

        // Establecer cookie para el servidor
        document.cookie = `user_language=${lang}; path=/; max-age=${86400 * 30}`;

        console.log('✅ Idioma cambiado exitosamente');

        // Recargar la página SIN query parameters para aplicar las traducciones
        window.location.href = window.location.origin + window.location.pathname;

    } catch (error) {
        console.error('💥 Error al cambiar idioma:', error);
    }
}

// Función para inicializar el idioma desde localStorage al cargar la página
function initializeLanguage() {
    const savedLang = localStorage.getItem('user_language');
    if (savedLang && ['es', 'en'].includes(savedLang)) {
        // Establecer cookie si hay idioma guardado en localStorage
        document.cookie = `user_language=${savedLang}; path=/; max-age=${86400 * 30}`;
        console.log('🌐 Idioma inicializado desde localStorage:', savedLang);
    }
}

// Inicializar el idioma cuando se carga la página
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 Inicializando sistema de idiomas...');
    initializeLanguage();

    // Configurar el selector de idioma del nav principal
    const languageSelector = document.getElementById('language-selector');
    const languageDropdown = document.getElementById('language-dropdown');

    console.log('🔍 Elementos encontrados:', {
        selector: !!languageSelector,
        dropdown: !!languageDropdown
    });

    if (languageSelector && languageDropdown) {
        console.log('✅ Configurando eventos del selector de idioma');

        // Marcar como configurado para evitar duplicados
        languageSelector.setAttribute('data-configured', 'true');

        // Función para mostrar/ocultar dropdown
        function toggleDropdown(e) {
            console.log('🖱️ Click en selector de idioma');
            e.preventDefault();
            e.stopPropagation();

            const isHidden = languageDropdown.classList.contains('hidden');
            languageDropdown.classList.toggle('hidden');

            console.log('📋 Dropdown visible:', !languageDropdown.classList.contains('hidden'));

            // Asegurar que el dropdown esté por encima de otros elementos
            if (!isHidden) {
                languageDropdown.style.zIndex = '9999';
            }
        }

        // Agregar múltiples tipos de eventos para mayor compatibilidad
        languageSelector.addEventListener('click', toggleDropdown);
        languageSelector.addEventListener('touchstart', toggleDropdown, { passive: false });

        // Cerrar el menú desplegable al hacer clic fuera de él
        document.addEventListener('click', function(e) {
            if (!languageSelector.contains(e.target) && !languageDropdown.contains(e.target)) {
                if (!languageDropdown.classList.contains('hidden')) {
                    languageDropdown.classList.add('hidden');
                }
            }
        });

        // También cerrar con Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !languageDropdown.classList.contains('hidden')) {
                languageDropdown.classList.add('hidden');
            }
        });

    } else {
        console.error('❌ No se encontraron los elementos del selector de idioma');
    }
});

// Backup: también intentar cuando la ventana esté completamente cargada
window.addEventListener('load', function() {
    // Solo ejecutar si no se configuró antes
    const languageSelector = document.getElementById('language-selector');
    const languageDropdown = document.getElementById('language-dropdown');

    if (languageSelector && languageDropdown && !languageSelector.hasAttribute('data-configured')) {
        console.log('🔄 Configurando selector de idioma en window.load');
        languageSelector.setAttribute('data-configured', 'true');

        languageSelector.addEventListener('click', function(e) {
            console.log('🖱️ Click en selector de idioma (window.load)');
            e.stopPropagation();
            languageDropdown.classList.toggle('hidden');
        });
    }
});
