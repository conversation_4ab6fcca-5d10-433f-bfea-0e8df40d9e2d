/**
 * Sistema de Historial - Manejo de la interfaz deslizante
 */

class HistorySystem {
    constructor() {
        this.isExpanded = false;
        this.historyBox = null;
        this.mainToolsContainer = null;
        this.toggleButton = null;
        this.historyList = null;
        this.isAuthenticated = false;

        // Inicializar de forma asíncrona con un pequeño delay para asegurar que el DOM esté listo
        setTimeout(() => {
            this.init().catch(error => {
                console.error('Error initializing history system:', error);
            });
        }, 100);
    }

    async init() {
        // Obtener elementos del DOM
        this.historyBox = document.getElementById('history-box');
        this.mainToolsContainer = document.getElementById('main-tools-container');
        this.toggleButton = document.getElementById('history-toggle');
        this.historyList = document.querySelector('.history-list');

        if (!this.historyBox || !this.mainToolsContainer || !this.toggleButton) {
            console.warn('Elementos del sistema de historial no encontrados');
            return;
        }

        // Configurar eventos
        this.setupEventListeners();

        // Establecer estado inicial
        this.setInitialState();

        // Verificar estado de autenticación
        await this.checkAuthenticationStatus();
    }

    setupEventListeners() {
        // Click en el botón de toggle
        this.toggleButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleHistory();
        });

        // Click en el header completo para toggle
        const historyHeader = document.querySelector('.history-header');
        if (historyHeader) {
            historyHeader.addEventListener('click', () => {
                this.toggleHistory();
            });
        }

        // Botón de historial integrado en la caja de herramientas
        const toolsHistoryBtn = document.getElementById('tools-history-btn');
        if (toolsHistoryBtn) {
            toolsHistoryBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleHistory();
            });
        }

        // Escuchar cambios de autenticación
        document.addEventListener('authStateChanged', (e) => {
            this.isAuthenticated = e.detail.isAuthenticated;
            if (this.isAuthenticated) {
                this.showHistoryBox();
                this.loadHistory();
                this.showToolsHistoryButton();
            } else {
                this.hideHistoryBox();
                this.hideToolsHistoryButton();
            }
        });

        // Escuchar cuando se genere un nuevo prompt
        document.addEventListener('promptGenerated', (e) => {
            if (this.isAuthenticated) {
                this.addToHistory(e.detail);
            }
        });
    }

    addCompressedIcon() {
        // Remover ícono existente si existe
        this.removeCompressedIcon();

        // Crear ícono para la caja comprimida
        const icon = document.createElement('div');
        icon.className = 'compressed-icon';
        icon.innerHTML = '🔧'; // Ícono de herramientas
        icon.title = window.translations?.back_to_tools || 'Volver a herramientas';

        // Agregar evento de clic
        icon.addEventListener('click', () => {
            this.collapseHistory();
        });

        this.mainToolsContainer.appendChild(icon);
    }

    removeCompressedIcon() {
        const existingIcon = this.mainToolsContainer.querySelector('.compressed-icon');
        if (existingIcon) {
            existingIcon.remove();
        }
    }

    setInitialState() {
        // Asegurar que el estado inicial sea correcto
        if (this.historyBox && this.mainToolsContainer) {
            // Historial colapsado (pequeño a la izquierda)
            this.historyBox.classList.add('history-collapsed');
            this.historyBox.classList.remove('history-expanded');

            // Herramientas en estado normal (centro)
            this.mainToolsContainer.classList.add('main-tools-normal');
            this.mainToolsContainer.classList.remove('main-tools-compressed');

            // Remover cualquier ícono existente
            this.removeCompressedIcon();

            this.isExpanded = false;
        }
    }

    toggleHistory() {
        if (!this.isAuthenticated) {
            console.warn('Usuario no autenticado');
            return;
        }

        this.isExpanded = !this.isExpanded;
        
        if (this.isExpanded) {
            this.expandHistory();
        } else {
            this.collapseHistory();
        }
    }

    expandHistory() {
        // Cambiar clases para expandir historial
        this.historyBox.classList.remove('history-collapsed');
        this.historyBox.classList.add('history-expanded');

        // Comprimir contenedor principal
        this.mainToolsContainer.classList.remove('main-tools-normal');
        this.mainToolsContainer.classList.add('main-tools-compressed');

        // Agregar ícono a la caja comprimida
        this.addCompressedIcon();

        // OCULTAR el botón de historial cuando está desplegado
        this.hideToolsHistoryButton();

        // Actualizar estado
        this.isExpanded = true;

        console.log('Historial expandido');
    }

    collapseHistory() {
        // Cambiar clases para colapsar historial
        this.historyBox.classList.remove('history-expanded');
        this.historyBox.classList.add('history-collapsed');

        // Restaurar contenedor principal
        this.mainToolsContainer.classList.remove('main-tools-compressed');
        this.mainToolsContainer.classList.add('main-tools-normal');

        // Remover ícono de la caja comprimida
        this.removeCompressedIcon();

        // MOSTRAR el botón de historial cuando se colapsa
        this.showToolsHistoryButton();

        // Actualizar estado
        this.isExpanded = false;

        console.log('Historial colapsado');
    }

    async checkAuthenticationStatus() {
        try {
            // Verificar si hay una sesión activa de Supabase
            if (typeof supabase !== 'undefined') {
                const { data: { session } } = await supabase.auth.getSession();
                this.isAuthenticated = !!session?.user;
            } else {
                // Fallback: verificar por elementos de UI
                const userProfileButton = document.getElementById('user-profile-button');
                this.isAuthenticated = userProfileButton && !userProfileButton.classList.contains('hidden');
            }

            if (this.isAuthenticated) {
                this.showHistoryBox();
                this.showToolsHistoryButton();
                this.loadHistory();
            } else {
                this.hideHistoryBox();
                this.hideToolsHistoryButton();
            }
        } catch (error) {
            console.error('Error checking auth status:', error);
            this.isAuthenticated = false;
            this.hideHistoryBox();
            this.hideToolsHistoryButton();
        }
    }

    showHistoryBox() {
        console.log('Mostrando caja de historial...');
        if (this.historyBox) {
            // Remover el display: none del CSS y mostrar la caja
            this.historyBox.style.display = 'block';
            this.historyBox.style.opacity = '1';
            // Pequeño delay para permitir que el display se aplique
            setTimeout(() => {
                this.historyBox.style.transform = 'translateX(0)';
                console.log('Caja de historial visible');
            }, 50);
        } else {
            console.warn('historyBox no encontrado');
        }
    }

    hideHistoryBox() {
        if (this.historyBox) {
            this.historyBox.style.opacity = '0';
            this.historyBox.style.transform = 'translateX(-100%)';
            // Ocultar completamente después de la transición
            setTimeout(() => {
                this.historyBox.style.display = 'none';
            }, 300);
        }
    }

    showToolsHistoryButton() {
        const toolsHistoryBtn = document.getElementById('tools-history-btn');
        if (toolsHistoryBtn) {
            // En móvil se muestra como flex, en desktop se oculta por CSS
            toolsHistoryBtn.classList.add('show');
        }
    }

    hideToolsHistoryButton() {
        const toolsHistoryBtn = document.getElementById('tools-history-btn');
        if (toolsHistoryBtn) {
            toolsHistoryBtn.classList.remove('show');
        }
    }

    async loadHistory() {
        if (!this.isAuthenticated) return;

        try {
            console.log('Cargando historial del usuario...');

            // Obtener token de Supabase
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) {
                this.showHistoryPlaceholder();
                return;
            }

            // Hacer petición a la API con JWT
            const response = await fetch('./api/history.php', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success && result.data.length > 0) {
                    this.displayHistory(result.data);
                } else {
                    this.showHistoryPlaceholder();
                }
            } else {
                console.error('Error al cargar historial:', response.status);
                this.showHistoryPlaceholder();
            }

        } catch (error) {
            console.error('Error al cargar historial:', error);
            this.showHistoryError();
        }
    }

    showHistoryPlaceholder() {
        if (!this.historyList) return;

        const placeholder = document.querySelector('.history-placeholder');
        if (placeholder) {
            placeholder.style.display = 'block';
        }
    }

    showHistoryError() {
        if (!this.historyList) return;

        this.historyList.innerHTML = `
            <div class="history-error text-center p-4">
                <p class="text-red-500 dark:text-red-400 text-sm">
                    Error al cargar el historial
                </p>
            </div>
        `;
    }

    displayHistory(historyData) {
        if (!this.historyList) return;

        // Limpiar contenido existente
        this.historyList.innerHTML = '';

        // Crear elementos para cada item del historial
        historyData.forEach(item => {
            const historyItem = this.createHistoryItem(item);
            this.historyList.appendChild(historyItem);
        });

        // Ocultar placeholder si existe
        const placeholder = document.querySelector('.history-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
        }
    }

    async addToHistory(promptData) {
        if (!this.isAuthenticated) return;

        try {
            console.log('Agregando al historial:', promptData);

            // Obtener token de Supabase
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) return;

            // Hacer petición a la API para guardar con JWT
            const response = await fetch('./api/history.php', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(promptData)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Recargar historial para mostrar el nuevo item
                    this.loadHistory();
                }
            } else {
                console.error('Error al guardar en historial:', response.status);
            }

        } catch (error) {
            console.error('Error al agregar al historial:', error);
        }
    }

    createHistoryItem(promptData) {
        const item = document.createElement('div');
        item.className = 'history-item bg-gray-50 dark:bg-gray-700 rounded-lg p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors';
        item.dataset.historyId = promptData.id;

        const title = promptData.prompt_title || `${promptData.tool_name} - ${promptData.tool_type}`;
        const content = promptData.prompt_content || '';
        const preview = content.length > 100 ? content.substring(0, 100) + '...' : content;

        item.innerHTML = `
            <div class="flex justify-between items-start mb-2">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate" title="${title}">
                    ${title}
                </h4>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                    ${this.formatDate(promptData.created_at)}
                </span>
            </div>
            <div class="flex items-center mb-2">
                <span class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                    ${promptData.tool_name}
                </span>
            </div>
            <p class="text-xs text-gray-600 dark:text-gray-300 line-clamp-2" title="${content}">
                ${preview}
            </p>
            <div class="flex justify-end mt-2 space-x-2">
                <button class="copy-btn text-xs text-blue-600 dark:text-blue-400 hover:underline" data-content="${content.replace(/"/g, '&quot;')}">
                    ${window.translations?.copy_prompt || 'Copiar'}
                </button>
                <button class="delete-btn text-xs text-red-600 dark:text-red-400 hover:underline" data-id="${promptData.id}">
                    ${window.translations?.delete_prompt || 'Eliminar'}
                </button>
            </div>
        `;

        // Agregar event listeners
        const copyBtn = item.querySelector('.copy-btn');
        const deleteBtn = item.querySelector('.delete-btn');

        copyBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.copyToClipboard(content);
        });

        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteHistoryItem(promptData.id);
        });

        return item;
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            // Mostrar notificación de éxito
            this.showNotification(window.translations?.copy_prompt || 'Copiado al portapapeles', 'success');
        } catch (error) {
            console.error('Error al copiar:', error);
            this.showNotification('Error al copiar', 'error');
        }
    }

    async deleteHistoryItem(historyId) {
        if (!confirm(window.translations?.confirm_delete || '¿Estás seguro de que quieres eliminar este prompt?')) {
            return;
        }

        try {
            // Obtener token de Supabase
            const { data: { session } } = await supabase.auth.getSession();
            if (!session) return;

            // Hacer petición a la API para eliminar con JWT
            const response = await fetch('./api/history.php', {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id: historyId })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    // Recargar historial
                    this.loadHistory();
                    this.showNotification(window.translations?.delete_success || 'Prompt eliminado', 'success');
                }
            } else {
                console.error('Error al eliminar:', response.status);
                this.showNotification('Error al eliminar', 'error');
            }

        } catch (error) {
            console.error('Error al eliminar del historial:', error);
            this.showNotification('Error al eliminar', 'error');
        }
    }

    showNotification(message, type = 'info') {
        // Crear notificación temporal
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm transition-all duration-300 ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remover después de 3 segundos
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return 'Hoy';
        } else if (diffDays === 2) {
            return 'Ayer';
        } else if (diffDays <= 7) {
            return `Hace ${diffDays} días`;
        } else {
            return date.toLocaleDateString();
        }
    }
}

// Inicializar el sistema de historial cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    window.historySystem = new HistorySystem();
});

// Exportar para uso global
window.HistorySystem = HistorySystem;
