-- =====================================================
-- ESQUEMA COMPLETO DE BASE DE DATOS - FastlyAI
-- =====================================================
-- Base de datos: fastlyai_db
-- Fecha de creación: 2025-01-25
-- Descripción: Sistema de créditos y gestión de usuarios para FastlyAI
-- =====================================================

-- Crear base de datos
CREATE DATABASE IF NOT EXISTS fastlyai_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE fastlyai_db;

-- =====================================================
-- TABLA: users
-- Descripción: Almacena información de usuarios y sus créditos
-- =====================================================
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    supabase_user_id VARCHAR(255) UNIQUE NOT NULL COMMENT 'ID del usuario en Supabase',
    email VARCHAR(255) NOT NULL COMMENT 'Email del usuario',
    full_name VARCHAR(255) COMMENT 'Nombre completo del usuario',
    avatar_url TEXT COMMENT 'URL del avatar del usuario',
    credits INT DEFAULT 0 COMMENT 'Créditos disponibles del usuario',
    total_credits_purchased INT DEFAULT 0 COMMENT 'Total de créditos comprados históricamente',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
    
    -- Índices
    INDEX idx_supabase_user_id (supabase_user_id),
    INDEX idx_email (email),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='Tabla principal de usuarios del sistema';

-- =====================================================
-- TABLA: pricing_plans
-- Descripción: Planes de precios disponibles para compra de créditos
-- =====================================================
CREATE TABLE pricing_plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'Nombre del plan (ej: Starter, Pro)',
    credits INT NOT NULL COMMENT 'Cantidad de créditos que incluye el plan',
    price_usd DECIMAL(10,2) NOT NULL COMMENT 'Precio en dólares USD',
    price_per_credit DECIMAL(10,3) NOT NULL COMMENT 'Precio por crédito individual',
    is_popular BOOLEAN DEFAULT FALSE COMMENT 'Si es el plan más popular (destacado)',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Si el plan está activo para venta',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
    
    -- Índices
    INDEX idx_active (is_active),
    INDEX idx_price (price_usd),
    INDEX idx_popular (is_popular)
) ENGINE=InnoDB COMMENT='Planes de precios para compra de créditos';

-- =====================================================
-- TABLA: credit_transactions
-- Descripción: Historial de todas las transacciones de créditos
-- =====================================================
CREATE TABLE credit_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'ID del usuario (FK a users.id)',
    transaction_type ENUM('purchase', 'usage', 'bonus', 'refund') NOT NULL COMMENT 'Tipo de transacción',
    credits_amount INT NOT NULL COMMENT 'Cantidad de créditos en la transacción',
    credits_before INT NOT NULL COMMENT 'Créditos antes de la transacción',
    credits_after INT NOT NULL COMMENT 'Créditos después de la transacción',
    description TEXT COMMENT 'Descripción de la transacción',
    reference_id VARCHAR(255) COMMENT 'ID de referencia externa (ej: prompt_usage.id)',
    stripe_session_id VARCHAR(255) COMMENT 'ID de sesión de Stripe para compras',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de la transacción',
    
    -- Claves foráneas
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at DESC),
    INDEX idx_stripe_session (stripe_session_id),
    INDEX idx_reference_id (reference_id)
) ENGINE=InnoDB COMMENT='Historial completo de transacciones de créditos';

-- =====================================================
-- TABLA: prompt_usage
-- Descripción: Registro de uso de prompts por parte de los usuarios
-- =====================================================
CREATE TABLE prompt_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'ID del usuario (FK a users.id)',
    tool_type ENUM('codigo', 'texto') NOT NULL COMMENT 'Tipo de herramienta (código o texto)',
    tool_name VARCHAR(100) NOT NULL COMMENT 'Nombre específico de la herramienta',
    prompt_content TEXT NOT NULL COMMENT 'Contenido del prompt generado',
    form_data JSON COMMENT 'Datos del formulario utilizados para generar el prompt',
    credits_used INT DEFAULT 1 COMMENT 'Créditos consumidos (normalmente 1)',
    ip_address VARCHAR(45) COMMENT 'Dirección IP del usuario',
    user_agent TEXT COMMENT 'User agent del navegador',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de uso del prompt',
    
    -- Claves foráneas
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_tool_type (tool_type),
    INDEX idx_tool_name (tool_name),
    INDEX idx_created_at (created_at DESC),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB COMMENT='Registro de uso de prompts y herramientas';

-- =====================================================
-- DATOS INICIALES
-- =====================================================

-- Insertar planes de precios por defecto
INSERT INTO pricing_plans (name, credits, price_usd, price_per_credit, is_popular, is_active) VALUES
('Starter', 100, 5.00, 0.050, FALSE, TRUE),
('Popular', 250, 10.00, 0.040, TRUE, TRUE),
('Pro', 500, 18.00, 0.036, FALSE, TRUE),
('Business', 1000, 30.00, 0.030, FALSE, TRUE);

-- =====================================================
-- TRIGGERS Y FUNCIONES
-- =====================================================

-- Trigger para actualizar updated_at automáticamente en users
DELIMITER $$
CREATE TRIGGER users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- Trigger para actualizar updated_at automáticamente en pricing_plans
DELIMITER $$
CREATE TRIGGER pricing_plans_updated_at 
    BEFORE UPDATE ON pricing_plans 
    FOR EACH ROW 
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- =====================================================
-- VISTAS ÚTILES
-- =====================================================

-- Vista para obtener estadísticas de usuarios
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.email,
    u.full_name,
    u.credits,
    u.total_credits_purchased,
    COUNT(pu.id) as total_prompts_used,
    SUM(pu.credits_used) as total_credits_consumed,
    u.created_at as user_since
FROM users u
LEFT JOIN prompt_usage pu ON u.id = pu.user_id
GROUP BY u.id;

-- Vista para transacciones recientes
CREATE VIEW recent_transactions AS
SELECT 
    ct.id,
    u.email,
    u.full_name,
    ct.transaction_type,
    ct.credits_amount,
    ct.description,
    ct.created_at
FROM credit_transactions ct
JOIN users u ON ct.user_id = u.id
ORDER BY ct.created_at DESC;

-- =====================================================
-- COMENTARIOS FINALES
-- =====================================================
-- Este esquema soporta:
-- 1. Autenticación con Supabase (solo auth)
-- 2. Sistema de créditos con compra y consumo
-- 3. Múltiples planes de precios
-- 4. Historial completo de transacciones
-- 5. Registro detallado de uso de prompts
-- 6. Integración con Stripe para pagos
-- 7. Estadísticas y reportes mediante vistas

-- Para restaurar este esquema:
-- 1. Ejecutar este archivo completo en MySQL
-- 2. Configurar las credenciales en include/database_config.php
-- 3. Verificar la conexión con Supabase para autenticación
