/**
 * Efecto typewriter con soporte para traducciones
 */

// Variable global para las frases (se inicializa desde PHP)
window.typewriterPhrases = window.typewriterPhrases || [];

// Configuración del efecto typewriter
let phraseIndex = 0;
let charIndex = 0;
let isDeleting = false;
let isEnd = false;
let typewriterElement = null;

function typeWriter() {
    if (!typewriterElement || !window.typewriterPhrases.length) {
        return;
    }

    isEnd = false;

    // Current phrase
    const currentPhrase = window.typewriterPhrases[phraseIndex];

    // Effect
    if (isDeleting) {
        // Remove char
        typewriterElement.textContent = currentPhrase.substring(0, charIndex - 1);
        charIndex--;
    } else {
        // Add char
        typewriterElement.textContent = currentPhrase.substring(0, charIndex + 1);
        charIndex++;
    }

    // If phrase is complete
    if (!isDeleting && charIndex === currentPhrase.length) {
        // Pause at end
        isEnd = true;
        isDeleting = true;
        setTimeout(typeWriter, 1500);
        return;
    }

    // If delete is complete
    if (isDeleting && charIndex === 0) {
        isDeleting = false;
        phraseIndex = (phraseIndex + 1) % window.typewriterPhrases.length;
    }

    // Speed
    let speed = isDeleting ? 60 : 120;

    // Pause between phrases
    if (isEnd) {
        speed = 500;
    }

    setTimeout(typeWriter, speed);
}

// Inicializar el efecto typewriter
function initTypewriter() {
    typewriterElement = document.querySelector('.typewriter');
    
    if (typewriterElement && window.typewriterPhrases.length > 0) {
        // Limpiar el contenido inicial
        typewriterElement.textContent = '';
        
        // Iniciar el efecto después de un pequeño delay
        setTimeout(typeWriter, 500);
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    initTypewriter();
});

// Función para reiniciar el efecto con nuevas frases (útil para cambio de idioma)
function restartTypewriter(newPhrases) {
    if (newPhrases && newPhrases.length > 0) {
        window.typewriterPhrases = newPhrases;
        phraseIndex = 0;
        charIndex = 0;
        isDeleting = false;
        isEnd = false;
        
        if (typewriterElement) {
            typewriterElement.textContent = '';
            setTimeout(typeWriter, 500);
        }
    }
}
