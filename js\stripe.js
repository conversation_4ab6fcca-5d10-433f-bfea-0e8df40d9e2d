/**
 * Sistema de pagos con Stripe para AccelPrompt
 */

class StripePaymentSystem {
    constructor() {
        this.stripe = null;
        this.isInitialized = false;
        this.isProcessing = false;
    }

    /**
     * Inicializar <PERSON>e con la clave pública
     */
    async init() {
        if (this.isInitialized) return;

        try {
            // Obtener la clave pública desde el backend
            const response = await fetch('./api/get_stripe_config.php');
            const config = await response.json();
            
            if (!config.success) {
                throw new Error('No se pudo obtener la configuración de Stripe');
            }

            // Inicializar Stripe
            this.stripe = Stripe(config.publishable_key);
            this.isInitialized = true;
            
            console.log('✅ Stripe inicializado correctamente');
            
        } catch (error) {
            console.error('❌ Error inicializando Stripe:', error);
            throw error;
        }
    }

    /**
     * Crear sesión de checkout y redirigir
     *
     * @param {number} credits Cantidad de créditos
     * @param {number} amount Precio en USD
     */
    async purchaseCredits(credits, amount) {
        if (this.isProcessing) {
            console.log('⏳ Ya hay un pago en proceso...');
            return;
        }

        try {
            this.isProcessing = true;

            // Verificar que el usuario esté autenticado
            if (!window.authSystem || !window.authSystem.isAuthenticated()) {
                this.showLoginRequiredModal();
                return;
            }

            // Obtener token de acceso
            const accessToken = await window.authSystem.getAccessToken();
            if (!accessToken) {
                throw new Error('No se pudo obtener el token de autenticación');
            }

            console.log(`💳 Iniciando compra: ${credits} créditos por $${amount}`);

            // Crear sesión de checkout
            const response = await fetch('./api/create_checkout_session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${accessToken}`
                },
                body: JSON.stringify({
                    credits: credits,
                    amount: amount
                })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.error || 'Error al crear la sesión de pago');
            }

            console.log('✅ Sesión de checkout creada:', result.session_id);

            // Redirigir a Stripe Checkout
            const { error } = await this.stripe.redirectToCheckout({
                sessionId: result.session_id
            });

            if (error) {
                throw new Error(error.message);
            }

        } catch (error) {
            console.error('❌ Error en el pago:', error);
            this.showErrorModal(error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Mostrar modal de login requerido
     */
    showLoginRequiredModal() {
        // Crear modal de login requerido si no existe
        let loginModal = document.getElementById('login-required-modal');

        if (!loginModal) {
            loginModal = document.createElement('div');
            loginModal.id = 'login-required-modal';
            loginModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden';
            loginModal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4">Inicia sesión para continuar</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Necesitas iniciar sesión para comprar créditos y usar AccelPrompt.
                            </p>
                        </div>
                        <div class="items-center px-4 py-3 space-y-2">
                            <button id="login-required-login-btn"
                                    class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                                Iniciar Sesión
                            </button>
                            <button id="close-login-required"
                                    class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-300">
                                Cancelar
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(loginModal);

            // Agregar event listeners
            document.getElementById('close-login-required').addEventListener('click', () => {
                loginModal.classList.add('hidden');
            });

            document.getElementById('login-required-login-btn').addEventListener('click', () => {
                loginModal.classList.add('hidden');
                if (window.authSystem && window.authSystem.showLoginModal) {
                    window.authSystem.showLoginModal();
                }
            });
        }

        // Mostrar el modal
        loginModal.classList.remove('hidden');
    }

    /**
     * Mostrar modal de error
     */
    showErrorModal(message) {
        // Crear modal de error si no existe
        let errorModal = document.getElementById('payment-error-modal');
        
        if (!errorModal) {
            errorModal = document.createElement('div');
            errorModal.id = 'payment-error-modal';
            errorModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden';
            errorModal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4">Error en el pago</h3>
                        <div class="mt-2 px-7 py-3">
                            <p id="payment-error-message" class="text-sm text-gray-500 dark:text-gray-400"></p>
                        </div>
                        <div class="items-center px-4 py-3">
                            <button id="close-payment-error" 
                                    class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300">
                                Cerrar
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(errorModal);

            // Agregar event listener para cerrar
            document.getElementById('close-payment-error').addEventListener('click', () => {
                errorModal.classList.add('hidden');
            });
        }

        // Mostrar el modal con el mensaje
        document.getElementById('payment-error-message').textContent = message;
        errorModal.classList.remove('hidden');
    }

    /**
     * Configurar botones de compra en la página
     */
    setupPurchaseButtons() {
        const purchaseButtons = document.querySelectorAll('[data-credits][data-amount]');
        
        purchaseButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                
                const credits = parseInt(button.dataset.credits);
                const amount = parseFloat(button.dataset.amount);
                
                if (!credits || !amount) {
                    console.error('❌ Datos de compra inválidos:', { credits, amount });
                    return;
                }

                // Inicializar Stripe si no está inicializado
                if (!this.isInitialized) {
                    await this.init();
                }

                // Procesar compra
                await this.purchaseCredits(credits, amount);
            });
        });

        console.log(`✅ ${purchaseButtons.length} botones de compra configurados`);
    }
}

// Inicializar sistema de pagos cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Crear instancia global del sistema de pagos
        window.stripePaymentSystem = new StripePaymentSystem();
        
        // Configurar botones de compra
        window.stripePaymentSystem.setupPurchaseButtons();
        
        console.log('💳 Sistema de pagos Stripe listo');
        
    } catch (error) {
        console.error('❌ Error inicializando sistema de pagos:', error);
    }
});

// Exponer funciones globalmente para compatibilidad
window.purchaseCredits = async (credits, amount) => {
    if (window.stripePaymentSystem) {
        if (!window.stripePaymentSystem.isInitialized) {
            await window.stripePaymentSystem.init();
        }
        await window.stripePaymentSystem.purchaseCredits(credits, amount);
    }
};
