<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../include/user_manager.php';

try {
    // Verificar método
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }
    
    // Obtener datos del request
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validar datos requeridos
    $requiredFields = ['user_id', 'plan_id', 'credits', 'price', 'payment_method'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field])) {
            throw new Exception("Campo requerido: $field");
        }
    }
    
    $userId = $input['user_id'];
    $planId = $input['plan_id'];
    $credits = intval($input['credits']);
    $price = floatval($input['price']);
    $paymentMethod = $input['payment_method'];
    
    // Validar que los créditos y precio sean positivos
    if ($credits <= 0 || $price <= 0) {
        throw new Exception('Créditos y precio deben ser positivos');
    }
    
    // Crear instancia del UserManager
    $userManager = new UserManager();
    
    // Verificar que el usuario existe
    $user = $userManager->getUserBySupabaseId($userId);
    if (!$user) {
        throw new Exception('Usuario no encontrado');
    }
    
    // Simular procesamiento de pago
    // En producción aquí integrarías con Stripe, PayPal, etc.
    if ($paymentMethod === 'test') {
        // Pago de prueba - siempre exitoso
        $paymentSuccessful = true;
        $transactionId = 'test_' . uniqid();
    } else {
        // Aquí iría la lógica real de procesamiento de pago
        throw new Exception('Método de pago no implementado');
    }
    
    if (!$paymentSuccessful) {
        throw new Exception('Error al procesar el pago');
    }
    
    // Agregar créditos al usuario
    $newCredits = $userManager->addCredits(
        $userId, 
        $credits, 
        "Compra de $credits créditos - Plan ID: $planId", 
        $transactionId
    );
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => 'Compra procesada exitosamente',
        'new_credits' => $newCredits,
        'credits_added' => $credits,
        'transaction_id' => $transactionId
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
