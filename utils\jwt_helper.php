<?php
require_once __DIR__ . '/../vendor/autoload.php';

use Firebase\JWT\JWT;
use Firebase\JWT\CachedKeySet;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Guz<PERSON>Http\Client;
use Guz<PERSON>Http\Psr7\HttpFactory;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

/**
 * Utilidad centralizada para manejo de JWT de Supabase
 * Proporciona funciones seguras para verificar y decodificar tokens JWT usando JWKS
 */
class JWTHelper {
    private static $supabaseUrl;
    private static $cachedKeySet;

    /**
     * Inicializar con la URL de Supabase y configurar JWKS
     */
    public static function init() {
        // Cargar variables de entorno si no están cargadas
        if (!isset($_ENV['SUPABASE_URL'])) {
            $dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
            $dotenv->load();
        }

        self::$supabaseUrl = $_ENV['SUPABASE_URL'] ?? null;

        if (!self::$supabaseUrl) {
            throw new Exception('SUPABASE_URL no está configurado en las variables de entorno');
        }

        // Configurar CachedKeySet para JWKS
        if (!self::$cachedKeySet) {
            $jwksUri = rtrim(self::$supabaseUrl, '/') . '/auth/v1/.well-known/jwks.json';
            $httpClient = new Client();
            $httpFactory = new HttpFactory();

            // Crear cache adapter para JWKS (cache por 1 hora)
            $cache = new FilesystemAdapter('jwks_cache', 3600);

            // Crear CachedKeySet con cache
            self::$cachedKeySet = new CachedKeySet(
                $jwksUri,
                $httpClient,
                $httpFactory,
                $cache
            );
        }
    }


    
    /**
     * Verificar y decodificar un token JWT de Supabase usando JWKS
     *
     * @param string $token El token JWT a verificar
     * @return array|false Retorna los datos del usuario si es válido, false si no
     */
    public static function verifyAndDecodeToken($token) {
        try {
            self::init();

            if (empty($token)) {
                return false;
            }

            // Remover 'Bearer ' si está presente
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            // Decodificar y verificar el token usando CachedKeySet
            // CachedKeySet maneja automáticamente la obtención de claves y verificación
            $decoded = JWT::decode($token, self::$cachedKeySet);

            // Convertir a array para facilitar el uso
            $payload = (array) $decoded;

            // Verificar que el token tenga los campos necesarios
            if (!isset($payload['sub'])) {
                return false;
            }

            return [
                'user_id' => $payload['sub'],
                'email' => $payload['email'] ?? null,
                'role' => $payload['role'] ?? null,
                'exp' => $payload['exp'] ?? null,
                'iat' => $payload['iat'] ?? null,
                'full_payload' => $payload
            ];

        } catch (ExpiredException $e) {
            error_log('JWT Token expirado: ' . $e->getMessage());
            return false;
        } catch (SignatureInvalidException $e) {
            error_log('JWT Firma inválida: ' . $e->getMessage());
            return false;
        } catch (Exception $e) {
            error_log('Error al verificar JWT: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Obtener el token JWT desde los headers de la solicitud
     * * @return string|null El token JWT o null si no se encuentra
     */
    public static function getTokenFromHeaders() {
        // En Apache, getallheaders() suele funcionar. En Nginx/PHP-FPM, $_SERVER['HTTP_AUTHORIZATION'] es más fiable.
        $headers = [];
        if (function_exists('getallheaders')) {
            $headers = getallheaders();
        } else {
            // Fallback para entornos donde getallheaders() no está disponible
            foreach ($_SERVER as $name => $value) {
                if (substr($name, 0, 5) == 'HTTP_') {
                    $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
                }
            }
        }
        
        // Buscar en el header Authorization
        if (isset($headers['Authorization'])) {
            return $headers['Authorization'];
        }
        
        // Buscar en el header authorization (minúsculas, algunos clientes pueden enviarlo así)
        if (isset($headers['authorization'])) {
            return $headers['authorization'];
        }
        
        return null;
    }
    
    /**
     * Verificar si un usuario está autenticado basado en el token JWT
     * * @return array|false Retorna los datos del usuario si está autenticado, false si no
     */
    public static function getCurrentUser() {
        $token = self::getTokenFromHeaders();
        
        if (!$token) {
            return false;
        }
        
        return self::verifyAndDecodeToken($token);
    }
    
    /**
     * Middleware para proteger endpoints que requieren autenticación
     * * @param bool $returnJson Si debe retornar JSON en caso de error (default: true)
     * @return array|void Retorna los datos del usuario o termina la ejecución si no está autenticado
     */
    public static function requireAuth($returnJson = true) {
        $user = self::getCurrentUser();
        
        if (!$user) {
            if ($returnJson) {
                http_response_code(401);
                header('Content-Type: application/json');
                echo json_encode([
                    'error' => 'Token de autenticación requerido o inválido.',
                    'status' => 401
                ]);
            } else {
                http_response_code(401);
                echo 'No autorizado';
            }
            exit;
        }
        
        return $user;
    }
}