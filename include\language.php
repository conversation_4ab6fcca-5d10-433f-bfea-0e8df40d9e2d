<?php
/**
 * Funciones para manejar la internacionalización
 */

// Idiomas soportados
$supported_languages = ['es', 'en'];

// Idioma por defecto
$default_language = 'es';

// Función para obtener el idioma actual (solo cookies y localStorage)
function getCurrentLanguage() {
    global $supported_languages, $default_language;

    // Verificar si hay un idioma en la cookie
    if (isset($_COOKIE['user_language']) && in_array($_COOKIE['user_language'], $supported_languages)) {
        return $_COOKIE['user_language'];
    }

    // Si no hay cookie, usar el idioma por defecto (español)
    // No detectamos automáticamente el idioma del navegador para evitar confusiones
    setcookie('user_language', $default_language, time() + (86400 * 30), "/");
    return $default_language;
}

// Función para cambiar el idioma
function setLanguage($lang) {
    global $supported_languages;

    if (in_array($lang, $supported_languages)) {
        setcookie('user_language', $lang, time() + (86400 * 30), "/");
        return true;
    }

    return false;
}

// Función para cargar las traducciones
function loadTranslations($lang) {
    global $supported_languages, $default_language;

    // Asegurarse de que el idioma sea válido
    if (!in_array($lang, $supported_languages)) {
        $lang = $default_language;
    }

    $translations_file = __DIR__ . "/../lang/{$lang}.php";

    if (file_exists($translations_file)) {
        return require $translations_file;
    }

    // Si no se encuentra el archivo, cargar el idioma por defecto
    $default_file = __DIR__ . "/../lang/{$default_language}.php";

    if (file_exists($default_file)) {
        return require $default_file;
    }

    // Si no hay archivos de traducción, devolver un array vacío
    return [];
}

// Obtener el idioma actual
$current_language = getCurrentLanguage();

// Cargar las traducciones para el idioma actual
$translations = loadTranslations($current_language);

// Función para traducir un texto
function t($key) {
    global $translations;

    // Devolver la traducción si existe
    if (isset($translations[$key])) {
        return $translations[$key];
    }

    // Si no existe, devolver la clave
    return $key;
}
