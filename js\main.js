/**
 * Archivo principal de JavaScript para index.php
 * Contiene la lógica de inicialización y manejo de modales
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar todos los modales
    initializeModals();
    
    // Limpiar URL de parámetros de idioma
    cleanLanguageFromURL();
});

/**
 * Inicializar todos los modales de la aplicación
 */
function initializeModals() {
    // Modal de Tally (feedback)
    initializeTallyModal();
    
    // Modal de límite de tasa excedido
    initializeRateLimitModal();
    
    // Modal de error genérico
    initializeErrorModal();
}

/**
 * Inicializar modal de Tally para feedback
 */
function initializeTallyModal() {
    const closeModalBtn = document.getElementById('close-tally-modal');
    const tallyModal = document.getElementById('tally-feedback-modal');

    if (closeModalBtn && tallyModal) {
        closeModalBtn.addEventListener('click', function() {
            tallyModal.classList.add('hidden');
        });

        // Cerrar modal al hacer clic fuera del contenido
        tallyModal.addEventListener('click', function(e) {
            if (e.target === tallyModal) {
                tallyModal.classList.add('hidden');
            }
        });
    }
}

/**
 * Inicializar modal de límite de tasa excedido
 */
function initializeRateLimitModal() {
    const closeRateLimitBtn = document.getElementById('close-rate-limit-modal');
    const rateLimitOkBtn = document.getElementById('rate-limit-ok-button');
    const rateLimitModal = document.getElementById('rate-limit-modal');

    if (closeRateLimitBtn && rateLimitModal) {
        closeRateLimitBtn.addEventListener('click', function() {
            rateLimitModal.classList.add('hidden');
        });

        if (rateLimitOkBtn) {
            rateLimitOkBtn.addEventListener('click', function() {
                rateLimitModal.classList.add('hidden');
            });
        }

        // Cerrar modal al hacer clic fuera del contenido
        rateLimitModal.addEventListener('click', function(e) {
            if (e.target === rateLimitModal) {
                rateLimitModal.classList.add('hidden');
            }
        });
    }
}

/**
 * Inicializar modal de error genérico
 */
function initializeErrorModal() {
    const closeErrorBtn = document.getElementById('close-error-modal');
    const errorOkBtn = document.getElementById('error-ok-button');
    const errorModal = document.getElementById('error-modal');

    if (closeErrorBtn && errorModal) {
        closeErrorBtn.addEventListener('click', function() {
            errorModal.classList.add('hidden');
        });

        if (errorOkBtn) {
            errorOkBtn.addEventListener('click', function() {
                errorModal.classList.add('hidden');
            });
        }

        // Cerrar modal al hacer clic fuera del contenido
        errorModal.addEventListener('click', function(e) {
            if (e.target === errorModal) {
                errorModal.classList.add('hidden');
            }
        });
    }
}

/**
 * Limpiar parámetros de idioma de la URL
 * Esto evita problemas con OAuth callbacks
 */
function cleanLanguageFromURL() {
    const url = new URL(window.location);
    if (url.searchParams.has('lang')) {
        url.searchParams.delete('lang');
        window.history.replaceState({}, document.title, url.toString());
    }
}

/**
 * Utilidad para mostrar modales programáticamente
 */
window.ModalUtils = {
    showTallyModal: function() {
        const modal = document.getElementById('tally-feedback-modal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    },
    
    showRateLimitModal: function() {
        const modal = document.getElementById('rate-limit-modal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    },
    
    showErrorModal: function() {
        const modal = document.getElementById('error-modal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    },
    
    hideModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }
};

/**
 * Función para reportar bugs (desktop)
 */
function reportBug() {
    // Abrir el modal de feedback con Tally
    const tallyModal = document.getElementById('tally-feedback-modal');
    if (tallyModal) {
        tallyModal.classList.remove('hidden');
    }
}

/**
 * Función para reportar bugs (móvil)
 */
function reportBugMobile() {
    // Abrir el modal de feedback con Tally
    const tallyModal = document.getElementById('tally-feedback-modal');
    if (tallyModal) {
        tallyModal.classList.remove('hidden');
    }
}
