document.addEventListener('DOMContentLoaded', () => {
    // Seleccionar todos los botones de copiar en la página
    const copyButtons = document.querySelectorAll('.copy-button');

    // Añadir evento a cada botón de ejemplo
    copyButtons.forEach(button => {
        button.addEventListener('click', async () => {
            // Obtener el contenido del prompt asociado a este botón
            const promptContainer = button.closest('.example-card').querySelector('.prompt-content');
            const promptText = promptContainer.textContent;

            try {
                await navigator.clipboard.writeText(promptText);
                // Cambiar el texto del botón temporalmente
                const originalText = button.textContent;
                button.textContent = '¡Copiado!';

                // Restaurar el texto original después de 2 segundos
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            } catch (err) {
                console.error('Error al copiar:', err);
            }
        });
    });

    // Manejar el botón de copiar del prompt final
    const copyPromptButton = document.getElementById('copy-prompt');
    if (copyPromptButton) {
        copyPromptButton.addEventListener('click', async () => {
            // Obtener el contenido del prompt final
            const finalPrompt = document.getElementById('final-prompt');
            const promptText = finalPrompt.textContent;

            try {
                await navigator.clipboard.writeText(promptText);
                // Cambiar el texto del botón temporalmente
                const originalText = copyPromptButton.textContent;
                copyPromptButton.textContent = '¡Copiado!';

                // Restaurar el texto original después de 2 segundos
                setTimeout(() => {
                    copyPromptButton.textContent = originalText;
                }, 2000);
            } catch (err) {
                console.error('Error al copiar:', err);
                // Fallback para navegadores que no soportan clipboard API
                try {
                    const textArea = document.createElement('textarea');
                    textArea.value = promptText;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    const originalText = copyPromptButton.textContent;
                    copyPromptButton.textContent = '¡Copiado!';
                    setTimeout(() => {
                        copyPromptButton.textContent = originalText;
                    }, 2000);
                } catch (fallbackErr) {
                    console.error('Error en fallback:', fallbackErr);
                }
            }
        });
    }
});
