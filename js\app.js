// Funciones globales para la autenticación (compatibilidad con nav.php)
// archivo principal inicio S
function showLoginModal() {
    if (window.authSystem) {
        window.authSystem.showLoginModal();
    }
}

function showRegisterModal() {
    if (window.authSystem) {
        window.authSystem.showRegisterModal(); // Fixed typo
    }
}

function logout() {
    if (window.authSystem) {
        window.authSystem.logout();
    }
}

document.addEventListener('DOMContentLoaded', () => {
    // El selector de idioma ahora se maneja en language.js
    const slides = document.querySelectorAll('.slide');
    const slidesContainer = document.querySelector('.slides');
    const progressBar = document.getElementById('progress-bar');
    const projectFormCodigo = document.getElementById('project-form-codigo');
    const projectFormTexto = document.getElementById('project-form-texto');
    const projectFormGeneral = document.getElementById('project-form-general');
    // El botón flotante ya no existe
    const backToFormButton = document.getElementById('back-to-form');
    const loadingOverlay = document.getElementById('loading-overlay');
    const policyCheckbox = document.getElementById('policy-checkbox');
    const copyButton = document.getElementById('copy-prompt');

    let currentStep = 1;
    const totalSteps = 4;
    let selectedType = null;
    let selectedTool = null;

    // El efecto typewriter ahora se maneja en typewriter.js

    // Inicializar visibilidad del checkbox al cargar la página
    updatePolicyCheckboxVisibility();

    // Escuchar cambios en el estado de autenticación
    if (window.authSystem) {
        // Verificar periódicamente el estado de autenticación
        setInterval(updatePolicyCheckboxVisibility, 1000);
    }

    // Validar checkbox de políticas
    function updateOptionCards() {
        const optionCards = document.querySelectorAll('.option-card');

        if (policyCheckbox && optionCards.length > 0) {
            if (policyCheckbox.checked) {
                optionCards.forEach(card => {
                    card.classList.remove('opacity-50', 'pointer-events-none');
                    card.removeAttribute('disabled');
                });
            } else {
                optionCards.forEach(card => {
                    card.classList.add('opacity-50', 'pointer-events-none');
                    card.setAttribute('disabled', 'true');
                });
            }
        }
    }

    // Manejar visibilidad del checkbox basado en autenticación
    function updatePolicyCheckboxVisibility() {
        const checkboxContainer = document.querySelector('.checkbox-container');

        if (checkboxContainer) {
            if (window.authSystem && window.authSystem.isAuthenticated()) {
                checkboxContainer.classList.remove('hidden-for-unauthenticated');
            } else {
                checkboxContainer.classList.add('hidden-for-unauthenticated');
            }
        }
    }

    // Escuchar cambios en el checkbox
    if (policyCheckbox) {
        policyCheckbox.addEventListener('change', updateOptionCards);
        // Inicializar estado
        updateOptionCards();
    }


    // Actualizar la barra de progreso
    function updateProgress() {
        const progress = (currentStep / totalSteps) * 100;
        progressBar.style.width = `${progress}%`;
    }

    // Esta función ya no es necesaria ya que eliminamos el botón flotante
    function updateBackButton() {
        // No hacemos nada, ya que los botones de retroceso están en cada slide
        // y se muestran/ocultan automáticamente con el slide
    }

    // Ajustar para dispositivos móviles - BREAKPOINT UNIFICADO
    function handleResize() {
        const slides = document.querySelectorAll('.slide');
        const slidesContainer = document.querySelector('.slides');

        if (!slides.length || !slidesContainer) return;

        const isMobile = window.innerWidth < 768; // CAMBIADO A 768px
        const slideWidth = slides[0].offsetWidth;

        // Si cambiamos de tamaño, ajustar la posición actual
        slidesContainer.style.transform = `translateX(-${(currentStep - 1) * slideWidth}px)`;

        // En móvil, hacer ajustes adicionales
        if (isMobile) {
            // Ajustar tamaño de los contenedores
            document.querySelectorAll('.input-large').forEach(input => {
                if (input) {
                    input.style.fontSize = '1rem';
                    input.style.padding = '0.75rem';
                }
            });

            // Ajustar espaciado
            document.querySelectorAll('.space-y-4').forEach(container => {
                if (container) {
                    container.classList.replace('space-y-4', 'space-y-3');
                }
            });
        }
    }

    // Escuchar cambios de tamaño solo si estamos en una página con slides
    if (document.querySelector('.slides')) {
        window.addEventListener('resize', handleResize);
        // Inicializar
        handleResize();
    }

    // Manejar la navegación entre slides
    function goToSlide(step) {
        console.log("Navegando al paso:", step);

        // Calcular la posición de desplazamiento
        const slideWidth = slides[0].offsetWidth;
        slidesContainer.style.transition = 'transform 0.5s ease';
        slidesContainer.style.transform = `translateX(-${(step - 1) * slideWidth}px)`;



        // Quitar la clase active de todos los slides
        slides.forEach(slide => {
            slide.classList.remove('active');
        });

        // Preparar los slides correctos según el paso
        if (step === 3) {
            // Para el paso 3, preparar el formulario correcto según el tipo seleccionado
            const codigoForm = document.querySelector('.slide[data-step="3"][data-form="codigo"]');
            const textoForm = document.querySelector('.slide[data-step="3"][data-form="texto"]');
            const generalForm = document.querySelector('.slide[data-step="3"][data-form="general"]');

            // Ocultar ambos primero
            if (codigoForm) codigoForm.style.display = 'none';
            if (textoForm) textoForm.style.display = 'none';
            if (generalForm) generalForm.style.display = 'none';

            // Mostrar el correcto
            if (selectedType === 'codigo' && codigoForm) {
                codigoForm.classList.add('active');
                codigoForm.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    codigoForm.style.display = 'flex';
                }, 500);
            } else if (selectedType === 'texto' && textoForm) {
                textoForm.classList.add('active');
                textoForm.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    textoForm.style.display = 'flex';
                }, 500);
            } else if (selectedType === 'general' && generalForm) {
                generalForm.classList.add('active');
                generalForm.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    generalForm.style.display = 'flex';
                }, 500);
            }
        } else if (step === 2) {
            // Para el paso 2, preparar la herramienta correspondiente según el tipo seleccionado
            const codigoTools = document.querySelector('.slide[data-step="2"][data-type="codigo"]');
            const textoTools = document.querySelector('.slide[data-step="2"][data-type="texto"]');
            const generalTools = document.querySelector('.slide[data-step="2"][data-type="general"]');

            console.log('🔍 Elementos encontrados para step 2:', {
                codigoTools: !!codigoTools,
                textoTools: !!textoTools,
                generalTools: !!generalTools,
                selectedType: selectedType
            });

            // Ocultar todos primero
            if (codigoTools) codigoTools.style.display = 'none';
            if (textoTools) textoTools.style.display = 'none';
            if (generalTools) generalTools.style.display = 'none';

            // Mostrar el correcto según el tipo seleccionado
            if (selectedType === 'codigo' && codigoTools) {
                codigoTools.classList.add('active');
                codigoTools.style.display = 'flex';
                console.log('✅ Mostrando herramientas de código');
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    codigoTools.style.display = 'flex';
                }, 500);
            } else if (selectedType === 'texto' && textoTools) {
                textoTools.classList.add('active');
                textoTools.style.display = 'flex';
                console.log('✅ Mostrando herramientas de texto');
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    textoTools.style.display = 'flex';
                }, 500);
            } else if (selectedType === 'general' && generalTools) {
                generalTools.classList.add('active');
                generalTools.style.display = 'flex';
                console.log('✅ Mostrando formulario General IA');
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    generalTools.style.display = 'flex';
                }, 500);
            } else {
                console.error('❌ No se pudo mostrar el slide correcto para step 2');
            }
        } else if (step === 3) {
            // Para el paso 3, solo manejar formularios de código y texto (general va directo al resultado)
            const codigoForm = document.querySelector('.slide[data-step="3"][data-form="codigo"]');
            const textoForm = document.querySelector('.slide[data-step="3"][data-form="texto"]');

            // Ocultar todos primero
            if (codigoForm) codigoForm.style.display = 'none';
            if (textoForm) textoForm.style.display = 'none';

            // Mostrar el correcto según el tipo seleccionado
            if (selectedType === 'codigo' && codigoForm) {
                codigoForm.classList.add('active');
                codigoForm.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    codigoForm.style.display = 'flex';
                }, 500);
            } else if (selectedType === 'texto' && textoForm) {
                textoForm.classList.add('active');
                textoForm.style.display = 'flex';
                setTimeout(() => {
                    // Asegurarse de que el slide esté visible después de la transición
                    textoForm.style.display = 'flex';
                }, 500);
            }
        } else {
            // Para otros pasos, activar el slide correspondiente
            const currentSlide = document.querySelector(`.slide[data-step="${step}"]`);
            if (currentSlide) {
                currentSlide.classList.add('active');
                currentSlide.style.display = 'flex';
                console.log(`🎯 Slide ${step} activado y mostrado`);
            } else {
                console.error(`❌ No se encontró el slide ${step}`);
            }
        }

        // Enfocar el campo de entrada si existe
        const activeSlide = document.querySelector('.slide.active');
        if (activeSlide) {
            const autofocusInput = activeSlide.querySelector('[data-autofocus]');
            if (autofocusInput) {
                setTimeout(() => autofocusInput.focus(), 600);
            }
        }

        currentStep = step;
        updateProgress();
        updateBackButton();
    }

    // Manejar la selección de opciones
    function handleOptionSelection(event) {
        const card = event.target.closest('.option-card');
        if (!card || card.hasAttribute('disabled')) return;

        // Verificar autenticación antes de permitir avanzar
        if (!window.authSystem || !window.authSystem.isAuthenticated()) {
            showLoginRequiredForSliders();
            return;
        }

        const currentSlide = card.closest('.slide');
        const step = parseInt(currentSlide.dataset.step);

        // Remover selección previa
        currentSlide.querySelectorAll('.option-card').forEach(c => c.classList.remove('selected'));
        card.classList.add('selected');

        // Guardar la selección y avanzar automáticamente
        if (step === 1) {
            selectedType = card.dataset.value;
            // Avanzar automáticamente después de una breve pausa
            setTimeout(() => goToSlide(2), 300);
        } else if (step === 2) {
            selectedTool = card.dataset.value;
            // Avanzar automáticamente después de una breve pausa
            setTimeout(() => goToSlide(3), 300);
        }
    }

    // Mostrar modal de login requerido para usar sliders
    function showLoginRequiredForSliders() {
        // Crear modal si no existe
        let loginModal = document.getElementById('slider-login-required-modal');

        if (!loginModal) {
            loginModal = document.createElement('div');
            loginModal.id = 'slider-login-required-modal';
            loginModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden';
            loginModal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4">${window.translations?.login_required_title || 'Inicia sesión para usar AccelPrompt'}</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                ${window.translations?.login_required_message || 'Necesitas iniciar sesión para generar prompts con nuestras herramientas de IA.'}
                            </p>
                        </div>
                        <div class="items-center px-4 py-3 space-y-2">
                            <button id="slider-login-btn"
                                    class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                                ${window.translations?.login_required_login_btn || 'Iniciar Sesión'}
                            </button>
                            <button id="close-slider-login"
                                    class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-300">
                                ${window.translations?.login_required_cancel_btn || 'Cancelar'}
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(loginModal);

            // Event listeners
            document.getElementById('close-slider-login').addEventListener('click', () => {
                loginModal.classList.add('hidden');
            });

            document.getElementById('slider-login-btn').addEventListener('click', () => {
                loginModal.classList.add('hidden');
                if (window.authSystem && window.authSystem.showLoginModal) {
                    window.authSystem.showLoginModal();
                }
            });
        }

        loginModal.classList.remove('hidden');
    }

    // Mostrar modal de créditos insuficientes
    function showInsufficientCreditsModal(creditsRemaining) {
        // Crear modal si no existe
        let creditsModal = document.getElementById('insufficient-credits-modal');

        if (!creditsModal) {
            creditsModal = document.createElement('div');
            creditsModal.id = 'insufficient-credits-modal';
            creditsModal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden';
            creditsModal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4">Créditos insuficientes</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                No tienes créditos suficientes para generar este prompt.
                            </p>
                            <p class="text-sm text-gray-700 dark:text-gray-300 mt-2">
                                <strong>Créditos restantes: <span id="credits-remaining-display">${creditsRemaining}</span></strong>
                            </p>
                        </div>
                        <div class="items-center px-4 py-3 space-y-2">
                            <button id="buy-credits-btn"
                                    class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                                Comprar Créditos
                            </button>
                            <button id="close-credits-modal"
                                    class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-300">
                                Cerrar
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(creditsModal);

            // Event listeners
            document.getElementById('close-credits-modal').addEventListener('click', () => {
                creditsModal.classList.add('hidden');
            });

            document.getElementById('buy-credits-btn').addEventListener('click', () => {
                creditsModal.classList.add('hidden');
                window.location.href = 'pricing.php';
            });
        }

        // Actualizar el número de créditos restantes
        const creditsDisplay = document.getElementById('credits-remaining-display');
        if (creditsDisplay) {
            creditsDisplay.textContent = creditsRemaining;
        }

        creditsModal.classList.remove('hidden');
    }

    // Actualizar display de créditos en la interfaz DINÁMICAMENTE
    function updateCreditsDisplay(creditsRemaining) {
        // Usar la función global del sistema de autenticación con animación
        if (window.authSystem && window.authSystem.updateCreditsInUI) {
            window.authSystem.updateCreditsInUI(creditsRemaining);
        } else {
            // Fallback con animación básica
            const creditsElements = document.querySelectorAll('.credits-display, #user-credits');
            creditsElements.forEach(element => {
                if (element) {
                    element.style.transition = 'all 0.3s ease';
                    element.style.transform = 'scale(1.05)';
                    element.innerHTML = `<span class="font-bold text-blue-600">${creditsRemaining}</span>`;

                    setTimeout(() => {
                        element.style.transform = 'scale(1)';
                    }, 300);
                }
            });
        }

        console.log(`💰 Créditos actualizados dinámicamente: ${creditsRemaining}`);
    }

    // Función para generar prompts con la API
    async function generatePromptWithAPI(formData) {
        try {
            loadingOverlay.classList.add('active');

            // Obtener token de acceso para autenticación
            const accessToken = await window.authSystem.getAccessToken();

            console.log('🔑 Token obtenido:', accessToken ? 'Presente' : 'Ausente');

            if (!accessToken) {
                throw new Error('No se pudo obtener el token de autenticación. Por favor, inicia sesión.');
            }

            // Crear el mensaje para enviar a la API
            let message = '';
            if (selectedType === 'codigo') {
                message = `Herramienta: ${selectedTool}\n\n`;
                message += `Crear un ${formData.projectType} con las siguientes características:\n`;
                formData.features.split(',').forEach(feature => {
                    message += `- ${feature.trim()}\n`;
                });
                message += `\nEstilo visual: ${formData.style}`;
            } else if (selectedType === 'texto') {
                message = `Herramienta: ${selectedTool}\n\n`;
                message += `Generar contenido para ${formData.contentType}\n`;
                message += `Características principales:\n`;
                formData.mainTopic.split(',').forEach(topic => {
                    message += `- ${topic.trim()}\n`;
                });
                message += `\nTono de voz: ${formData.tone}`;
            } else if (selectedType === 'general') {
                message = `Tipo de prompt: ${formData.promptType}\n\n`;
                message += `Idea o concepto: ${formData.idea}\n\n`;
                message += `Genera un prompt optimizado para cualquier modelo de IA que pueda ayudarme con esta tarea.`;
            }

            console.log('📤 Enviando petición a Gemini AI con token de autorización...');

            // Usar API de Gemini como principal (generate_prompt.php con OpenRouter queda como respaldo)
            const response = await fetch('./api/generate_prompt_g.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${accessToken}`
                },
                body: JSON.stringify({
                    message: message
                })
            });

            const data = await response.json();
            console.log('Respuesta de la API:', data);

            if (!response.ok) {
                // Verificar si es un error de límite excedido
                if (response.status === 429) {
                    // Mostrar el modal de límite excedido
                    const rateLimitModal = document.getElementById('rate-limit-modal');
                    if (rateLimitModal) {
                        rateLimitModal.classList.remove('hidden');
                    }
                    throw new Error('Límite de solicitudes excedido');
                } else if (response.status === 402) {
                    // Error de créditos insuficientes
                    showInsufficientCreditsModal(data.credits_remaining || 0);
                    throw new Error('Créditos insuficientes');
                } else {
                    // Mostrar el modal de error genérico
                    const errorModal = document.getElementById('error-modal');
                    const errorMessage = document.getElementById('error-message');
                    if (errorModal && errorMessage) {
                        errorMessage.textContent = `Error: ${response.statusText || 'Error en la solicitud'}`;
                        errorModal.classList.remove('hidden');
                    }
                    throw new Error(`Error en la solicitud: ${response.statusText}`);
                }
            }

            if (data.prompt) {
                console.log('Prompt generado:', data.prompt);

                // Actualizar contador de créditos DINÁMICAMENTE
                if (data.credits_remaining !== undefined) {
                    updateCreditsDisplay(data.credits_remaining);
                } else {
                    // Si no viene en la respuesta, recargar desde API
                    if (window.authSystem && window.authSystem.refreshCreditsFromAPI) {
                        setTimeout(() => {
                            window.authSystem.refreshCreditsFromAPI();
                        }, 500); // Pequeño delay para que se procese la transacción
                    }
                }

                return data.prompt;
            } else if (data.error) {
                // Verificar si el error es por límite excedido
                if (data.status === 429) {
                    // Mostrar el modal de límite excedido
                    const rateLimitModal = document.getElementById('rate-limit-modal');
                    if (rateLimitModal) {
                        rateLimitModal.classList.remove('hidden');
                    }
                    throw new Error('Límite de solicitudes excedido');
                } else {
                    // Mostrar el modal de error genérico
                    const errorModal = document.getElementById('error-modal');
                    const errorMessage = document.getElementById('error-message');
                    if (errorModal && errorMessage) {
                        errorMessage.textContent = data.error || 'Error al generar el prompt';
                        errorModal.classList.remove('hidden');
                    }
                    throw new Error(data.error || 'Error al generar el prompt');
                }
            } else {
                // Mostrar el modal de error genérico
                const errorModal = document.getElementById('error-modal');
                const errorMessage = document.getElementById('error-message');
                if (errorModal && errorMessage) {
                    errorMessage.textContent = 'Error al generar el prompt: respuesta inesperada del servidor';
                    errorModal.classList.remove('hidden');
                }
                throw new Error('Error al generar el prompt: respuesta inesperada del servidor');
            }
        } catch (error) {
            console.error('Error:', error);

            // Si el error no es de límite excedido y no se ha mostrado un modal específico
            if (!error.message.includes('Límite de solicitudes excedido') && document.getElementById('error-modal').classList.contains('hidden')) {
                // Mostrar el modal de error genérico
                const errorModal = document.getElementById('error-modal');
                const errorMessage = document.getElementById('error-message');
                if (errorModal && errorMessage) {
                    errorMessage.textContent = error.message || 'Ha ocurrido un error al procesar tu solicitud';
                    errorModal.classList.remove('hidden');
                }
            }

            // Devolver un mensaje de error para mostrar en la interfaz
            return "No se pudo generar el prompt debido a un error. Por favor, inténtalo de nuevo más tarde.";
        } finally {
            loadingOverlay.classList.remove('active');
        }
    }

    // Event Listeners
    document.addEventListener('click', handleOptionSelection);

    // Manejar el formulario para codificación
    projectFormCodigo.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = {
            projectType: projectFormCodigo.querySelector('[name="projectType"]').value,
            features: projectFormCodigo.querySelector('[name="features"]').value,
            style: projectFormCodigo.querySelector('[name="style"]').value
        };

        const finalPrompt = await generatePromptWithAPI(formData);
        document.getElementById('final-prompt').textContent = finalPrompt;

        // Guardar en historial si el usuario está autenticado
        if (window.historySystem && window.historySystem.isAuthenticated && finalPrompt) {
            const historyData = {
                tool_type: 'coding',
                tool_name: selectedTool || 'Herramienta de código',
                prompt_title: `${selectedTool} - ${formData.projectType}`,
                prompt_content: finalPrompt,
                form_data: formData
            };

            // Disparar evento para que el sistema de historial lo capture
            document.dispatchEvent(new CustomEvent('promptGenerated', {
                detail: historyData
            }));
        }

        goToSlide(4);
    });

    // Manejar el formulario para escritura
    projectFormTexto.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = {
            contentType: projectFormTexto.querySelector('[name="contentType"]').value,
            mainTopic: projectFormTexto.querySelector('[name="mainTopic"]').value,
            tone: projectFormTexto.querySelector('[name="tone"]').value
        };

        const finalPrompt = await generatePromptWithAPI(formData);
        document.getElementById('final-prompt').textContent = finalPrompt;

        // Guardar en historial si el usuario está autenticado
        if (window.historySystem && window.historySystem.isAuthenticated && finalPrompt) {
            const historyData = {
                tool_type: 'writing',
                tool_name: selectedTool || 'Herramienta de escritura',
                prompt_title: `${selectedTool} - ${formData.contentType}`,
                prompt_content: finalPrompt,
                form_data: formData
            };

            // Disparar evento para que el sistema de historial lo capture
            document.dispatchEvent(new CustomEvent('promptGenerated', {
                detail: historyData
            }));
        }

        goToSlide(4);
    });

    // Manejar el formulario para IA general
    if (projectFormGeneral) {
        console.log('✅ Formulario General IA encontrado y configurado');
        projectFormGeneral.addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log('📝 Enviando formulario General IA...');

            const formData = {
                promptType: projectFormGeneral.querySelector('[name="promptType"]').value,
                idea: projectFormGeneral.querySelector('[name="idea"]').value
            };

            console.log('📝 Datos del formulario General IA:', formData);

            const finalPrompt = await generatePromptWithAPI(formData);
            console.log('🎯 Prompt final generado:', finalPrompt);
            
            const finalPromptElement = document.getElementById('final-prompt');
            if (finalPromptElement) {
                finalPromptElement.textContent = finalPrompt;
                console.log('✅ Prompt asignado al elemento final-prompt');
            } else {
                console.error('❌ No se encontró el elemento final-prompt');
            }

            // Guardar en historial si el usuario está autenticado
            if (window.historySystem && window.historySystem.isAuthenticated && finalPrompt) {
                const historyData = {
                    tool_type: 'general',
                    tool_name: 'IA General',
                    prompt_title: `${formData.promptType} - ${formData.idea.substring(0, 50)}...`,
                    prompt_content: finalPrompt,
                    form_data: formData
                };

                // Disparar evento para que el sistema de historial lo capture
                document.dispatchEvent(new CustomEvent('promptGenerated', {
                    detail: historyData
                }));
            }

            goToSlide(4);
        });
    } else {
        console.error('❌ No se encontró el formulario General IA');
    }

    // Botones de navegación
    // El botón flotante ya no existe, así que no necesitamos este listener
    // backButton.addEventListener('click', () => {
    //     goToSlide(currentStep - 1);
    // });

    // Botones de retroceso en cada slide
    const backButton2 = document.getElementById('back-button-2');
    const backButton3 = document.getElementById('back-button-3');
    const backButton4 = document.getElementById('back-button-4');
    const backButtonCodigo = document.getElementById('back-button-codigo');
    const backButtonTexto = document.getElementById('back-button-texto');
    const backButtonFinal = document.getElementById('back-button-final');

    // Botones del paso 2 (vuelven al paso 1)
    if (backButton2) {
        backButton2.addEventListener('click', () => {
            goToSlide(1);
        });
    }

    if (backButton3) {
        backButton3.addEventListener('click', () => {
            goToSlide(1);
        });
    }

    if (backButton4) {
        backButton4.addEventListener('click', () => {
            goToSlide(1);
        });
    }

    // Botones del paso 3 (vuelven al paso 2)
    if (backButtonCodigo) {
        backButtonCodigo.addEventListener('click', () => {
            goToSlide(2);
        });
    }

    if (backButtonTexto) {
        backButtonTexto.addEventListener('click', () => {
            goToSlide(2);
        });
    }

    // Botón del paso 4 (vuelve al paso 3)
    if (backButtonFinal) {
        backButtonFinal.addEventListener('click', () => {
            goToSlide(3);
        });
    }



    backToFormButton.addEventListener('click', () => {
        goToSlide(3);
    });

        // Copiar el prompt
        copyButton.addEventListener('click', async () => {
            const promptText = document.getElementById('final-prompt').textContent;
            try {
                await navigator.clipboard.writeText(promptText);
                copyButton.textContent = '¡Copiado!';

                // Mostrar el modal personalizado en lugar de abrir Tally directamente
                const tallyModal = document.getElementById('tally-feedback-modal');
                if (tallyModal) {
                    tallyModal.classList.remove('hidden');
                }

                setTimeout(() => {
                    copyButton.textContent = 'Copiar Prompt';
                }, 2000);

                // Comentado el código anterior de Tally
                // Tally.openPopup('n9xMp4', {
                //     width: 372,
                //     emoji: { text: '👋', animation: 'flash' },
                // });
            } catch (err) {
                console.error('Error al copiar:', err);
            }
        });

    // Inicializar
    // Asegurarse de que solo el primer slide esté visible al cargar
    slides.forEach((slide, index) => {
        if (index === 0) {
            slide.style.display = 'flex';
            slide.classList.add('active');
        } else {
            slide.style.display = 'none';
            slide.classList.remove('active');
        }
    });

    updateProgress();
    updateBackButton();
});