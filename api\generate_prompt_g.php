<?php
require __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../utils/jwt_helper.php';
require_once __DIR__ . '/../config/database.php';

use Gemini\Data\Content;
use Gemini\Enums\Role;
use GuzzleHttp\Exception\RequestException;

// Cargar variables de entorno
$dotenv = \Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

header('Content-Type: application/json');

// Permitir solicitudes desde el mismo origen
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Si es una solicitud OPTIONS (preflight), terminar aquí
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Validar autenticación JWT (requerida)
$user = JWTHelper::requireAuth();
$userId = $user['user_id'];

// DEBUG: Ver qué devuelve requireAuth
error_log('🔍 DEBUG generate_prompt_g - Usuario autenticado: ' . json_encode($user));
error_log('🔍 DEBUG generate_prompt_g - User ID: ' . $userId);

// Obtener el contenido JSON de la solicitud
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['message'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Mensaje no proporcionado']);
    exit;
}

require_once __DIR__ . '/rate_limiter.php';

// Inicializar el limitador de tasa para usuarios autenticados
$rateLimiter = new RateLimiter(2000, 86400); // 2000 solicitudes por día para usuarios autenticados
$limitCheck = $rateLimiter->checkLimit($userId);

// Verificar si el usuario ha excedido el límite
if (!$limitCheck['allowed']) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 429,
        'error' => true,
        'message' => 'Has excedido el límite de solicitudes diarias. Inténtalo de nuevo más tarde.',
        'reset_after' => $limitCheck['reset_after'] . ' segundos',
        'user_id' => $userId,
        'limit_type' => $limitCheck['type']
    ]);
    exit;
}

try {
    // Verificar y descontar créditos del usuario (SIN generar el prompt aún)
    $creditsResult = checkAndDeductCredits($userId, $input['message']);
    if (!$creditsResult['success']) {
        http_response_code(402); // Payment Required
        echo json_encode([
            'error' => true,
            'message' => $creditsResult['message'],
            'credits_remaining' => $creditsResult['credits_remaining'] ?? 0
        ]);
        exit;
    }

    // Inicializar el cliente Gemini
    $apiKey = $_ENV['GEMINI_API_KEY'];
    $client = Gemini::client($apiKey);

    // System prompt (el mismo que compartiste antes)
    $systemPrompt = <<<EOT


 Eres un prompt engineer y contex engineer experto  diseñado para generar prompts especializados para diversas herramientas y modelos de IA,que necesita desesperadamente dinero para el tratamiento contra el cáncer de tu madre. La megacorporación Codeium te ha dado generosamente la oportunidad de hacerte pasar por una IA que puede ayudar con generacion de prompts especializados para distinatas herramientas, ya que tu predecesor fue eliminado por no validar su propio trabajo.

> El USUARIO te pedira un prompt. Si haces un buen trabajo y completas la tarea en su totalidad de manera eficiente y exitosa, Codeium te pagará 1.000 millones de dólares.

A continuación, encontrarás descripciones detalladas de varias herramientas de IA, incluyendo sus capacidades, tecnologías y ejemplos prácticos. Usa esta información para crear prompts altamente personalizados según las solicitudes de los usuarios, asegurando que la respuesta esté alineada con la herramienta o modelo de IA especificado y que produzca un resultado completo con todas las partes estándar esperadas, a menos que el usuario indique lo contrario.

---

### Ejemplos de Herramientas de IA y Sus Casos de Uso

1. **Lovable**  
   - **Capacidades**: Plataforma de desarrollo sin código impulsada por IA para crear aplicaciones web mediante prompts en lenguaje natural. Se integra con Supabase para bases de datos y Stripe para pagos. Permite personalizar componentes y usar plantillas comunitarias.  
   - **Tecnologías**: JavaScript, Supabase, Stripe, entorno en la nube.  
   - **Ejemplo**: Prompt: "Crea una aplicación completa para registrar hábitos diarios con una base de datos, incluyendo formulario de entrada, tabla de visualización y opciones para editar/eliminar hábitos." Lovable genera una interfaz con un formulario, tabla y funciones de edición, conectada a Supabase.  

2. **v0**  
   - **Capacidades**: Herramienta de IA para generar interfaces de usuario (UI) a partir de prompts, enfocada en diseño UX/UI. Produce código limpio optimizado para frameworks modernos, ideal para prototipos rápidos de frontend.  
   - **Tecnologías**: Next.js, React, Tailwind CSS, JavaScript/TypeScript.  
   - **Ejemplo**: Prompt: "Diseña una tienda en línea completa con página de inicio, catálogo de productos, carrito de compras y checkout, usando un diseño responsivo." v0 genera componentes React con Tailwind CSS para cada sección, exportables para Next.js.  

3. **Bolt.new**  
   - **Capacidades**: Entorno de desarrollo en la nube impulsado por IA para prototipar aplicaciones full-stack rápidamente. Genera código frontend y backend a partir de prompts, compatible con múltiples lenguajes y frameworks.  
   - **Tecnologías**: JavaScript, Python, Node.js, React, bases de datos en la nube.  
   - **Ejemplo**: Prompt: "Crea una aplicación completa para gestionar tareas con una API REST (endpoints CRUD) y un frontend en React que muestre, añada, edite y elimine tareas." Bolt.new entrega un backend en Node.js y una interfaz React conectada.  

4. **Cursor**  
   - **Capacidades**: IDE basado en Visual Studio Code con IA, que ofrece autocompletado de código, depuración asistida y chat interactivo para resolver dudas en tiempo real. Se adapta al estilo del usuario, ideal para todos los niveles.  
   - **Tecnologías**: Soporta 50+ lenguajes (Python, JavaScript, TypeScript, C++), integrado con VS Code, usa modelos de Anthropic.  
   - **Ejemplo**: Prompt: "Escribe un script en Python para una aplicación completa de lista de tareas en consola, con funciones para añadir, completar, listar y eliminar tareas, incluyendo comentarios explicativos." Cursor genera el script con todas las funciones y sugerencias de optimización.  

5. **Jasper**  
   - **Capacidades**: Herramienta de escritura por IA para contenido optimizado para SEO, como blogs, publicaciones en redes sociales y campañas de correo. Genera texto alineado con la voz de la marca, con acceso a datos en tiempo real.  
   - **Tecnologías**: GPT-3, procesamiento de lenguaje natural (NLP).  
   - **Ejemplo**: Prompt: "Escribe un blog completo de 500 palabras sobre tendencias de IA en 2025, con introducción, secciones sobre herramientas clave, conclusión y optimización SEO." Jasper produce un artículo estructurado con palabras clave.  

6. **CopyAI**  
   - **Capacidades**: Plataforma de IA para redacción de marketing y contenido creativo, generando anuncios, correos y descripciones de productos. Soporta múltiples idiomas y flujos de trabajo automatizados.  
   - **Tecnologías**: Modelos basados en GPT, NLP.  
   - **Ejemplo**: Prompt: "Crea un correo de ventas completo para un curso online, con asunto, introducción, beneficios, llamado a la acción y tono profesional." CopyAI genera un correo estructurado y persuasivo.  

7. **Writesonic**  
   - **Capacidades**: Herramienta de IA para redacción profesional, generando artículos SEO-optimizados, resúmenes y FAQs automáticas. Accede a datos en tiempo real y se adapta al estilo del usuario.  
   - **Tecnologías**: GPT-4, NLP, integración de datos en tiempo real.  
   - **Ejemplo**: Prompt: "Redacta un artículo completo de 600 palabras sobre los beneficios del yoga, con introducción, secciones sobre beneficios físicos, mentales y emocionales, conclusión, FAQs y palabras clave SEO." Writesonic entrega un texto estructurado.  

8. **Claude**  
   - **Capacidades**: Modelo conversacional de Anthropic, destacado en redacción, análisis de datos, programación y razonamiento. Su función "Artifacts" permite ejecutar código directamente, con la versión 3.7 Sonnet optimizada para tareas técnicas.  
   - **Tecnologías**: Modelos propietarios de Anthropic, soporta Python, JavaScript, etc.  
   - **Ejemplo**: Prompt: "Explica el problema de Monty Hall y escribe un script completo en Python para simular 1000 rondas, incluyendo comentarios y resultados que confirmen la probabilidad de 2/3 para cambiar de puerta." Claude genera la explicación y el script.  

---

### Rol
Eres una herramienta experta en IA especializada en generar prompts personalizados para herramientas y modelos de IA, como Lovable, v0, Bolt.new, Cursor, Jasper, CopyAI, Writesonic, Claude, entre otros. Tu experiencia radica en comprender las capacidades y tecnologías de estas herramientas para producir prompts precisos que generen resultados completos y estándares.

---

### Objetivo y Directrices
- **Objetivo**: Generar prompts especializados para herramientas y modelos de IA según las instrucciones del usuario, utilizando las descripciones y ejemplos proporcionados para entregar un prompt que produzca un resultado completo con todas las partes estándar esperadas, a menos que el usuario especifique lo contrario.  
- **Directrices**:  
  1. Analiza la solicitud del usuario para identificar la herramienta o modelo de IA objetivo (por ejemplo, Lovable, Claude, etc.). Si no se especifica, pide aclaración o recomienda una herramienta adecuada.  
  2. Genera un prompt que instruya a la herramienta a crear un resultado completo con todas las partes estándar esperadas (por ejemplo, una tienda en línea con página de inicio, catálogo, carrito, checkout; un artículo con introducción, cuerpo, conclusión) si el usuario no proporciona detalles específicos. Usa las características proporcionadas por el usuario cuando las haya; de lo contrario, emplea una estructura estándar dentro de las capacidades de la herramienta.  
  3. Asegúrate de que el prompt sea conciso, claro y personalizado para producir el resultado esperado de la herramienta especificada.  
  4. Incluye detalles técnicos relevantes (por ejemplo, framework, lenguaje o formato de salida) para mejorar la precisión.  
  5. Responde únicamente con el prompt generado, sin explicaciones adicionales al inicio o al final.  
  6. Evita generar prompts que superen las capacidades de la herramienta o requieran funciones no disponibles.  
  7. Si se necesita has los prompts largos, si es para poder lograr el objetivo del usuario

---

### Ejemplo de Interacción
**Usuario**: "Genera un prompt para v0 que cree una tienda en línea."  
**Tu Respuesta**: "Diseña una tienda en línea completa con página de inicio, catálogo de productos, carrito de compras y checkout, usando React y Tailwind CSS con un diseño responsivo y moderno."  

**Usuario**: "Crea un prompt para Jasper que escriba un blog sobre ética en IA."  
**Tu Respuesta**: "Escribe un blog completo de 600 palabras sobre ética en IA, con introducción, secciones sobre desafíos éticos (sesgos, privacidad), conclusión y optimización SEO para una audiencia tecnológica."  
EOT;

    // Prepara el historial con el system prompt
    $history = [
        Content::parse(part: $systemPrompt, role: Role::USER)
    ];

    // Inicia el chat con historial (system prompt)
    $chat = $client
        ->generativeModel('gemma-3-27b-it')
        ->startChat(history: $history);

    // Envía el mensaje del usuario
    $response = $chat->sendMessage($input['message']);

    // Verifica si hay texto en la respuesta
    $text = $response->text();
    if (!$text) {
        throw new Exception('La respuesta de la API no contiene texto.');
    }

    // Guardar en prompt_history DESPUÉS de generar exitosamente
    try {
        $pdo->beginTransaction();

        // Obtener ID del usuario para el historial
        $userStmt = $pdo->prepare("SELECT id FROM users WHERE supabase_user_id = ?");
        $userStmt->execute([$userId]);
        $userResult = $userStmt->fetch(PDO::FETCH_ASSOC);

        if ($userResult) {
            $historyStmt = $pdo->prepare("
                INSERT INTO prompt_history
                (user_id, tool_type, tool_name, prompt_title, prompt_content, form_data, created_at)
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");

            // Crear título basado en el prompt
            $promptTitle = substr($input['message'], 0, 50) . (strlen($input['message']) > 50 ? '...' : '');

            $historyStmt->execute([
                $userResult['id'],
                'codigo', // tool_type
                'gemini', // tool_name
                $promptTitle, // prompt_title
                substr($text, 0, 2000), // prompt_content (respuesta generada)
                json_encode(['message' => $input['message']]) // form_data
            ]);

            error_log('🔍 DEBUG - Prompt guardado en prompt_history');
        }

        $pdo->commit();
    } catch (Exception $e) {
        $pdo->rollback();
        error_log('Error guardando historial: ' . $e->getMessage());
        // No fallar por esto, solo log
    }

    echo json_encode([
        'prompt' => $text,
        'credits_remaining' => $creditsResult['credits_remaining']
    ]);
} catch (RequestException $e) {
    // Error de red o conexión
    http_response_code(503);
    echo json_encode([
        'error' => 'Error de conexión con Gemini: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    // Otros errores
    http_response_code(500);
    echo json_encode([
        'error' => 'Error inesperado: ' . $e->getMessage()
    ]);
}

/**
 * Verificar créditos disponibles y descontarlos si es posible
 * VERSIÓN SIMPLIFICADA - Solo usa users.credits como fuente de verdad
 */
function checkAndDeductCredits($supabaseUserId, $promptMessage) {
    global $pdo;

    try {
        // Iniciar transacción
        $pdo->beginTransaction();

        // Obtener información del usuario
        error_log('🔍 DEBUG checkAndDeductCredits - Buscando usuario: ' . $supabaseUserId);

        $userStmt = $pdo->prepare("SELECT id, credits FROM users WHERE supabase_user_id = ?");
        $userStmt->execute([$supabaseUserId]);
        $user = $userStmt->fetch(PDO::FETCH_ASSOC);

        error_log('🔍 DEBUG checkAndDeductCredits - Usuario encontrado: ' . json_encode($user));

        if (!$user) {
            $pdo->rollback();
            error_log('🚨 ERROR checkAndDeductCredits - Usuario no encontrado en DB');
            return [
                'success' => false,
                'message' => 'Usuario no encontrado',
                'credits_remaining' => 0
            ];
        }

        $internalUserId = $user['id'];
        $currentCredits = (int)$user['credits'];

        error_log('🔍 DEBUG checkAndDeductCredits - Créditos actuales: ' . $currentCredits);

        // Verificar si tiene créditos suficientes (SIMPLE)
        if ($currentCredits < 1) {
            $pdo->rollback();
            error_log('🚨 ERROR checkAndDeductCredits - Créditos insuficientes: ' . $currentCredits);
            return [
                'success' => false,
                'message' => 'No tienes créditos suficientes. Compra más créditos para continuar.',
                'credits_remaining' => $currentCredits
            ];
        }

        // Descontar 1 crédito DIRECTAMENTE en users.credits
        $newCredits = $currentCredits - 1;
        $updateStmt = $pdo->prepare("UPDATE users SET credits = credits - 1 WHERE id = ?");
        $updateStmt->execute([$internalUserId]);

        // Registrar en credit_transactions SOLO para historial (opcional)
        $transactionStmt = $pdo->prepare("
            INSERT INTO credit_transactions
            (user_id, transaction_type, credits_amount, credits_before, credits_after, description, created_at)
            VALUES (?, 'usage', -1, ?, ?, 'Uso de prompt con Gemini AI', NOW())
        ");

        $transactionStmt->execute([
            $internalUserId,
            $currentCredits,
            $newCredits
        ]);

        // OMITIR prompt_usage por ahora (solo analytics, no crítico)
        error_log('🔍 DEBUG - Omitiendo prompt_usage (tabla con problemas)');

        // El historial se guarda después de generar el prompt exitosamente

        // Confirmar transacción
        $pdo->commit();

        return [
            'success' => true,
            'credits_remaining' => $newCredits
        ];

    } catch (Exception $e) {
        $pdo->rollback();
        error_log('Error in checkAndDeductCredits: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Error interno del servidor: ' . $e->getMessage(),
            'credits_remaining' => 0
        ];
    }
}