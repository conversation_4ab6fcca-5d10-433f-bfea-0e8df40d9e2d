[27-Jul-2025 23:46:03 Europe/Berlin] 🚀 SUCCESS.PHP - ARCHIVO INICIADO
[27-Jul-2025 23:46:03 Europe/Berlin] 🚀 SUCCESS.PHP - <PERSON><PERSON><PERSON><PERSON><PERSON> cargado
[27-Jul-2025 23:46:03 Europe/Berlin] 🚀 SUCCESS.PHP - Database config cargado
[27-Jul-2025 23:46:03 Europe/Berlin] ✅ SUCCESS.PHP - Objeto PDO inicializado y conectado.
[27-Jul-2025 23:46:03 Europe/Berlin] ✅ SUCCESS.PHP - Conexión a DB funcional (consulta de prueba exitosa).
[27-Jul-2025 23:46:03 Europe/Berlin] 🚀 SUCCESS.PHP - Variables inicializadas
[27-Jul-2025 23:46:03 Europe/Berlin] 🚀 SUCCESS.PHP - Session ID recibido de GET: cs_test_a1xftBrg9dIvre3MgjJdI1TvNJEva3z2RDgUzXEPwWzT1LckOgKXuNmuTN
[27-Jul-2025 23:46:03 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando procesamiento principal.
[27-Jul-2025 23:46:03 Europe/Berlin] 🔥 SUCCESS.PHP - Session ID presente. Obteniendo sesión de Stripe...
[27-Jul-2025 23:46:04 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado completo de retrieveCheckoutSession: {"success":true,"session":{"id":"cs_test_a1xftBrg9dIvre3MgjJdI1TvNJEva3z2RDgUzXEPwWzT1LckOgKXuNmuTN","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":2000,"amount_total":2000,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http:\/\/localhost:8000\/pricing.php?canceled=1","client_reference_id":null,"client_secret":null,"collected_information":null,"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":null,"customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"VE","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"elsokas","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":null,"invoice_creation":{"enabled":false,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":null,"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"},"mode":"payment","origin_context":null,"payment_intent":"pi_3RpcOKBMyajljax83OnK2LLk","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":null,"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http:\/\/localhost:8000\/success.php?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null},"payment_status":"paid","metadata":{"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}}
[27-Jul-2025 23:46:04 Europe/Berlin] 🔥 SUCCESS.PHP - Sesión de Stripe obtenida exitosamente.
[27-Jul-2025 23:46:04 Europe/Berlin] 🔥 SUCCESS.PHP - Payment status de Stripe: paid
[27-Jul-2025 23:46:04 Europe/Berlin] 🔥 SUCCESS.PHP - Metadata de Stripe: {"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[27-Jul-2025 23:46:04 Europe/Berlin] 🔥 SUCCESS.PHP - Condición: payment_status === "paid" CUMPLIDA. Procediendo a agregar créditos...
[27-Jul-2025 23:46:04 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Metadata raw: {"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[27-Jul-2025 23:46:04 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - User ID extraído: aa41cf96-4ef0-4573-bf3f-f43e181bd59f
[27-Jul-2025 23:46:04 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Credits extraídos (int): 500
[27-Jul-2025 23:46:04 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Iniciando procesamiento de créditos para User ID: aa41cf96-4ef0-4573-bf3f-f43e181bd59f, Créditos: 500, Sesión: cs_test_a1xftBrg9dIvre3MgjJdI1TvNJEva3z2RDgUzXEPwWzT1LckOgKXuNmuTN
[27-Jul-2025 23:46:04 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando iniciar transacción DB.
[27-Jul-2025 23:46:04 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción DB iniciada.
[27-Jul-2025 23:46:04 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Check Transacción: SELECT id FROM credit_transactions WHERE stripe_session_id = ? AND user_id = (SELECT id FROM users WHERE supabase_user_id = ?)
[27-Jul-2025 23:46:04 Europe/Berlin] ❌ ADD_CREDITS_FROM_SESSION - Rollback ejecutado debido a una excepción.
[27-Jul-2025 23:46:04 Europe/Berlin] ❌ SUCCESS.PHP - Error CRÍTICO en addCreditsFromSession (Excepción): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'stripe_session_id' in 'where clause' en C:\Users\<USER>\Desktop\micro-saas-php\success.php línea 218
[27-Jul-2025 23:46:04 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado final de addCreditsFromSession: FALLÓ
[27-Jul-2025 23:47:27 Europe/Berlin] 🚀 SUCCESS.PHP - ARCHIVO INICIADO
[27-Jul-2025 23:47:27 Europe/Berlin] 🚀 SUCCESS.PHP - StripeHelper cargado
[27-Jul-2025 23:47:27 Europe/Berlin] 🚀 SUCCESS.PHP - Database config cargado
[27-Jul-2025 23:47:27 Europe/Berlin] ✅ SUCCESS.PHP - Objeto PDO inicializado y conectado.
[27-Jul-2025 23:47:27 Europe/Berlin] ✅ SUCCESS.PHP - Conexión a DB funcional (consulta de prueba exitosa).
[27-Jul-2025 23:47:27 Europe/Berlin] 🚀 SUCCESS.PHP - Variables inicializadas
[27-Jul-2025 23:47:27 Europe/Berlin] 🚀 SUCCESS.PHP - Session ID recibido de GET: cs_test_a1BZRalWHwa05RXwmbRtXgURflu4X7KJLVcWR96M2rJ9reV9oqnHs5Pa74
[27-Jul-2025 23:47:27 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando procesamiento principal.
[27-Jul-2025 23:47:27 Europe/Berlin] 🔥 SUCCESS.PHP - Session ID presente. Obteniendo sesión de Stripe...
[27-Jul-2025 23:47:28 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado completo de retrieveCheckoutSession: {"success":true,"session":{"id":"cs_test_a1BZRalWHwa05RXwmbRtXgURflu4X7KJLVcWR96M2rJ9reV9oqnHs5Pa74","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":2000,"amount_total":2000,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http:\/\/localhost:8000\/pricing.php?canceled=1","client_reference_id":null,"client_secret":null,"collected_information":null,"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":null,"customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"VE","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"elsokas","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":null,"invoice_creation":{"enabled":false,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":null,"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"},"mode":"payment","origin_context":null,"payment_intent":"pi_3RpcPfBMyajljax80I7zbX52","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":null,"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http:\/\/localhost:8000\/success.php?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null},"payment_status":"paid","metadata":{"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}}
[27-Jul-2025 23:47:28 Europe/Berlin] 🔥 SUCCESS.PHP - Sesión de Stripe obtenida exitosamente.
[27-Jul-2025 23:47:28 Europe/Berlin] 🔥 SUCCESS.PHP - Payment status de Stripe: paid
[27-Jul-2025 23:47:28 Europe/Berlin] 🔥 SUCCESS.PHP - Metadata de Stripe: {"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[27-Jul-2025 23:47:28 Europe/Berlin] 🔥 SUCCESS.PHP - Condición: payment_status === "paid" CUMPLIDA. Procediendo a agregar créditos...
[27-Jul-2025 23:47:28 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Metadata raw: {"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[27-Jul-2025 23:47:28 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - User ID extraído: aa41cf96-4ef0-4573-bf3f-f43e181bd59f
[27-Jul-2025 23:47:28 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Credits extraídos (int): 500
[27-Jul-2025 23:47:28 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Iniciando procesamiento de créditos para User ID: aa41cf96-4ef0-4573-bf3f-f43e181bd59f, Créditos: 500, Sesión: cs_test_a1BZRalWHwa05RXwmbRtXgURflu4X7KJLVcWR96M2rJ9reV9oqnHs5Pa74
[27-Jul-2025 23:47:28 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando iniciar transacción DB.
[27-Jul-2025 23:47:28 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción DB iniciada.
[27-Jul-2025 23:47:28 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Check Transacción: SELECT id FROM credit_transactions WHERE stripe_session_id = ? AND user_id = (SELECT id FROM users WHERE supabase_user_id = ?)
[27-Jul-2025 23:47:28 Europe/Berlin] ❌ ADD_CREDITS_FROM_SESSION - Rollback ejecutado debido a una excepción.
[27-Jul-2025 23:47:28 Europe/Berlin] ❌ SUCCESS.PHP - Error CRÍTICO en addCreditsFromSession (Excepción): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'stripe_session_id' in 'where clause' en C:\Users\<USER>\Desktop\micro-saas-php\success.php línea 218
[27-Jul-2025 23:47:28 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado final de addCreditsFromSession: FALLÓ
[28-Jul-2025 00:22:25 Europe/Berlin] 🚀 SUCCESS.PHP - ARCHIVO INICIADO
[28-Jul-2025 00:22:26 Europe/Berlin] 🚀 SUCCESS.PHP - StripeHelper cargado
[28-Jul-2025 00:22:26 Europe/Berlin] 🚀 SUCCESS.PHP - Database config cargado
[28-Jul-2025 00:22:26 Europe/Berlin] ✅ SUCCESS.PHP - Objeto PDO inicializado y conectado.
[28-Jul-2025 00:22:26 Europe/Berlin] ✅ SUCCESS.PHP - Conexión a DB funcional (consulta de prueba exitosa).
[28-Jul-2025 00:22:26 Europe/Berlin] 🚀 SUCCESS.PHP - Variables inicializadas
[28-Jul-2025 00:22:26 Europe/Berlin] 🚀 SUCCESS.PHP - Session ID recibido de GET: cs_test_a1BZRalWHwa05RXwmbRtXgURflu4X7KJLVcWR96M2rJ9reV9oqnHs5Pa74
[28-Jul-2025 00:22:26 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando procesamiento principal.
[28-Jul-2025 00:22:26 Europe/Berlin] 🔥 SUCCESS.PHP - Session ID presente. Obteniendo sesión de Stripe...
[28-Jul-2025 00:22:28 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado completo de retrieveCheckoutSession: {"success":true,"session":{"id":"cs_test_a1BZRalWHwa05RXwmbRtXgURflu4X7KJLVcWR96M2rJ9reV9oqnHs5Pa74","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":2000,"amount_total":2000,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http:\/\/localhost:8000\/pricing.php?canceled=1","client_reference_id":null,"client_secret":null,"collected_information":null,"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":null,"customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"VE","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"elsokas","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":null,"invoice_creation":{"enabled":false,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":null,"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"},"mode":"payment","origin_context":null,"payment_intent":"pi_3RpcPfBMyajljax80I7zbX52","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":null,"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http:\/\/localhost:8000\/success.php?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null},"payment_status":"paid","metadata":{"credits":"500","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}}
[28-Jul-2025 00:22:46 Europe/Berlin] 🚀 SUCCESS.PHP - ARCHIVO INICIADO
[28-Jul-2025 00:22:46 Europe/Berlin] 🚀 SUCCESS.PHP - StripeHelper cargado
[28-Jul-2025 00:22:46 Europe/Berlin] 🚀 SUCCESS.PHP - Database config cargado
[28-Jul-2025 00:22:46 Europe/Berlin] ✅ SUCCESS.PHP - Objeto PDO inicializado y conectado.
[28-Jul-2025 00:22:46 Europe/Berlin] ✅ SUCCESS.PHP - Conexión a DB funcional (consulta de prueba exitosa).
[28-Jul-2025 00:22:46 Europe/Berlin] 🚀 SUCCESS.PHP - Variables inicializadas
[28-Jul-2025 00:22:46 Europe/Berlin] 🚀 SUCCESS.PHP - Session ID recibido de GET: cs_test_a1uwxrAawa6xeUmHVQiYu2nm5lVSwJPdvKMGg4aPD2LLTXM54Oxj4RO0Gy
[28-Jul-2025 00:22:46 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando procesamiento principal.
[28-Jul-2025 00:22:46 Europe/Berlin] 🔥 SUCCESS.PHP - Session ID presente. Obteniendo sesión de Stripe...
[28-Jul-2025 00:22:47 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado completo de retrieveCheckoutSession: {"success":true,"session":{"id":"cs_test_a1uwxrAawa6xeUmHVQiYu2nm5lVSwJPdvKMGg4aPD2LLTXM54Oxj4RO0Gy","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http:\/\/localhost:8000\/pricing.php?canceled=1","client_reference_id":null,"client_secret":null,"collected_information":null,"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":null,"customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"VE","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"elsokas","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":null,"invoice_creation":{"enabled":false,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":null,"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"},"mode":"payment","origin_context":null,"payment_intent":"pi_3RpcxrBMyajljax83IZIhgNA","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":null,"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http:\/\/localhost:8000\/success.php?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null},"payment_status":"paid","metadata":{"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}}
[28-Jul-2025 00:22:47 Europe/Berlin] 🔥 SUCCESS.PHP - Sesión de Stripe obtenida exitosamente.
[28-Jul-2025 00:22:47 Europe/Berlin] 🔥 SUCCESS.PHP - Payment status de Stripe: paid
[28-Jul-2025 00:22:47 Europe/Berlin] 🔥 SUCCESS.PHP - Metadata de Stripe: {"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 00:22:48 Europe/Berlin] 🔥 SUCCESS.PHP - Condición: payment_status === "paid" CUMPLIDA. Procediendo a agregar créditos...
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Metadata raw: {"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - User ID extraído: aa41cf96-4ef0-4573-bf3f-f43e181bd59f
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Credits extraídos (int): 100
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Iniciando procesamiento de créditos para User ID: aa41cf96-4ef0-4573-bf3f-f43e181bd59f, Créditos: 100, Sesión: cs_test_a1uwxrAawa6xeUmHVQiYu2nm5lVSwJPdvKMGg4aPD2LLTXM54Oxj4RO0Gy
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando iniciar transacción DB.
[28-Jul-2025 00:22:48 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción DB iniciada.
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Check Transacción: SELECT id FROM credit_transactions WHERE stripe_session_id = ? AND user_id = (SELECT id FROM users WHERE supabase_user_id = ?)
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Check Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 00:22:48 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción NO procesada previamente.
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Obtener Usuario: SELECT id, credits FROM users WHERE supabase_user_id = ?
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Obtener Usuario ejecutado. Resultado: TRUE
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Resultado fetch usuario: {"id":3,"credits":4}
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Datos de usuario obtenidos: Internal ID: 3, Créditos actuales: 4, Nuevos créditos: 104
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Actualizar Créditos: UPDATE users SET credits = ?, total_credits_purchased = total_credits_purchased + ? WHERE id = ?
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Actualizar Créditos ejecutado. Resultado: TRUE
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Insertar Transacción: INSERT INTO credit_transactions (user_id, transaction_type, credits_amount, credits_before, credits_after, description, stripe_session_id, created_at) VALUES (?, 'purchase', ?, ?, ?, ?, ?, NOW())
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Insertar Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 00:22:48 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando commitear transacción DB.
[28-Jul-2025 00:22:48 Europe/Berlin] ✅ SUCCESS.PHP - Transacción DB commiteada exitosamente.
[28-Jul-2025 00:22:48 Europe/Berlin] ✅ SUCCESS.PHP - Créditos agregados exitosamente al usuario 3.
[28-Jul-2025 00:22:48 Europe/Berlin]    - Créditos antes: 4
[28-Jul-2025 00:22:48 Europe/Berlin]    - Créditos después: 104
[28-Jul-2025 00:22:48 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado final de addCreditsFromSession: EXITOSO
[28-Jul-2025 01:16:01 Europe/Berlin] 🚀 SUCCESS.PHP - ARCHIVO INICIADO
[28-Jul-2025 01:16:01 Europe/Berlin] 🚀 SUCCESS.PHP - StripeHelper cargado
[28-Jul-2025 01:16:01 Europe/Berlin] 🚀 SUCCESS.PHP - Database config cargado
[28-Jul-2025 01:16:01 Europe/Berlin] ✅ SUCCESS.PHP - Objeto PDO inicializado y conectado.
[28-Jul-2025 01:16:01 Europe/Berlin] ✅ SUCCESS.PHP - Conexión a DB funcional (consulta de prueba exitosa).
[28-Jul-2025 01:16:01 Europe/Berlin] 🚀 SUCCESS.PHP - Variables inicializadas
[28-Jul-2025 01:16:01 Europe/Berlin] 🚀 SUCCESS.PHP - Session ID recibido de GET: cs_test_a1SrkHnE2GcCggXyp7J0cDgr9bEbMlvFWFTrhhXfMYR7ijG7gakm2AJxNu
[28-Jul-2025 01:16:01 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando procesamiento principal.
[28-Jul-2025 01:16:01 Europe/Berlin] 🔥 SUCCESS.PHP - Session ID presente. Obteniendo sesión de Stripe...
[28-Jul-2025 01:16:03 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado completo de retrieveCheckoutSession: {"success":true,"session":{"id":"cs_test_a1SrkHnE2GcCggXyp7J0cDgr9bEbMlvFWFTrhhXfMYR7ijG7gakm2AJxNu","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http:\/\/localhost:8000\/pricing.php?canceled=1","client_reference_id":null,"client_secret":null,"collected_information":null,"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":null,"customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"VE","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"elsokas","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":null,"invoice_creation":{"enabled":false,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":null,"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"},"mode":"payment","origin_context":null,"payment_intent":"pi_3RpdnOBMyajljax83hgKeKmU","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":null,"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http:\/\/localhost:8000\/success.php?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null},"payment_status":"paid","metadata":{"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}}
[28-Jul-2025 01:16:03 Europe/Berlin] 🔥 SUCCESS.PHP - Sesión de Stripe obtenida exitosamente.
[28-Jul-2025 01:16:03 Europe/Berlin] 🔥 SUCCESS.PHP - Payment status de Stripe: paid
[28-Jul-2025 01:16:03 Europe/Berlin] 🔥 SUCCESS.PHP - Metadata de Stripe: {"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 01:16:03 Europe/Berlin] 🔥 SUCCESS.PHP - Condición: payment_status === "paid" CUMPLIDA. Procediendo a agregar créditos...
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Metadata raw: {"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - User ID extraído: aa41cf96-4ef0-4573-bf3f-f43e181bd59f
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Credits extraídos (int): 100
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Iniciando procesamiento de créditos para User ID: aa41cf96-4ef0-4573-bf3f-f43e181bd59f, Créditos: 100, Sesión: cs_test_a1SrkHnE2GcCggXyp7J0cDgr9bEbMlvFWFTrhhXfMYR7ijG7gakm2AJxNu
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando iniciar transacción DB.
[28-Jul-2025 01:16:03 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción DB iniciada.
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Check Transacción: SELECT id FROM credit_transactions WHERE stripe_session_id = ? AND user_id = (SELECT id FROM users WHERE supabase_user_id = ?)
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Check Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:03 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción NO procesada previamente.
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Obtener Usuario: SELECT id, credits FROM users WHERE supabase_user_id = ?
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Obtener Usuario ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Resultado fetch usuario: {"id":3,"credits":103}
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Datos de usuario obtenidos: Internal ID: 3, Créditos actuales: 103, Nuevos créditos: 203
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Actualizar Créditos: UPDATE users SET credits = ?, total_credits_purchased = total_credits_purchased + ? WHERE id = ?
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Actualizar Créditos ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Insertar Transacción: INSERT INTO credit_transactions (user_id, transaction_type, credits_amount, credits_before, credits_after, description, stripe_session_id, created_at) VALUES (?, 'purchase', ?, ?, ?, ?, ?, NOW())
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Insertar Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:03 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando commitear transacción DB.
[28-Jul-2025 01:16:03 Europe/Berlin] ✅ SUCCESS.PHP - Transacción DB commiteada exitosamente.
[28-Jul-2025 01:16:03 Europe/Berlin] ✅ SUCCESS.PHP - Créditos agregados exitosamente al usuario 3.
[28-Jul-2025 01:16:03 Europe/Berlin]    - Créditos antes: 103
[28-Jul-2025 01:16:03 Europe/Berlin]    - Créditos después: 203
[28-Jul-2025 01:16:03 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado final de addCreditsFromSession: EXITOSO
[28-Jul-2025 01:16:44 Europe/Berlin] 🚀 SUCCESS.PHP - ARCHIVO INICIADO
[28-Jul-2025 01:16:44 Europe/Berlin] 🚀 SUCCESS.PHP - StripeHelper cargado
[28-Jul-2025 01:16:44 Europe/Berlin] 🚀 SUCCESS.PHP - Database config cargado
[28-Jul-2025 01:16:44 Europe/Berlin] ✅ SUCCESS.PHP - Objeto PDO inicializado y conectado.
[28-Jul-2025 01:16:44 Europe/Berlin] ✅ SUCCESS.PHP - Conexión a DB funcional (consulta de prueba exitosa).
[28-Jul-2025 01:16:44 Europe/Berlin] 🚀 SUCCESS.PHP - Variables inicializadas
[28-Jul-2025 01:16:44 Europe/Berlin] 🚀 SUCCESS.PHP - Session ID recibido de GET: cs_test_a10PQd0Q6Yo6vmbLNKgkAzPAEhA9sQR0ZVCNeuYfT877bCGIbjZTuK49Lu
[28-Jul-2025 01:16:44 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando procesamiento principal.
[28-Jul-2025 01:16:44 Europe/Berlin] 🔥 SUCCESS.PHP - Session ID presente. Obteniendo sesión de Stripe...
[28-Jul-2025 01:16:46 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado completo de retrieveCheckoutSession: {"success":true,"session":{"id":"cs_test_a10PQd0Q6Yo6vmbLNKgkAzPAEhA9sQR0ZVCNeuYfT877bCGIbjZTuK49Lu","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":3500,"amount_total":3500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http:\/\/localhost:8000\/pricing.php?canceled=1","client_reference_id":null,"client_secret":null,"collected_information":null,"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":null,"customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"VE","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"elsokas","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":null,"invoice_creation":{"enabled":false,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":null,"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"credits":"1000","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"},"mode":"payment","origin_context":null,"payment_intent":"pi_3Rpdo6BMyajljax809rjKytu","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":null,"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http:\/\/localhost:8000\/success.php?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null},"payment_status":"paid","metadata":{"credits":"1000","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}}
[28-Jul-2025 01:16:46 Europe/Berlin] 🔥 SUCCESS.PHP - Sesión de Stripe obtenida exitosamente.
[28-Jul-2025 01:16:46 Europe/Berlin] 🔥 SUCCESS.PHP - Payment status de Stripe: paid
[28-Jul-2025 01:16:46 Europe/Berlin] 🔥 SUCCESS.PHP - Metadata de Stripe: {"credits":"1000","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 01:16:46 Europe/Berlin] 🔥 SUCCESS.PHP - Condición: payment_status === "paid" CUMPLIDA. Procediendo a agregar créditos...
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Metadata raw: {"credits":"1000","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - User ID extraído: aa41cf96-4ef0-4573-bf3f-f43e181bd59f
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Credits extraídos (int): 1000
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Iniciando procesamiento de créditos para User ID: aa41cf96-4ef0-4573-bf3f-f43e181bd59f, Créditos: 1000, Sesión: cs_test_a10PQd0Q6Yo6vmbLNKgkAzPAEhA9sQR0ZVCNeuYfT877bCGIbjZTuK49Lu
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando iniciar transacción DB.
[28-Jul-2025 01:16:46 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción DB iniciada.
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Check Transacción: SELECT id FROM credit_transactions WHERE stripe_session_id = ? AND user_id = (SELECT id FROM users WHERE supabase_user_id = ?)
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Check Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:46 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción NO procesada previamente.
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Obtener Usuario: SELECT id, credits FROM users WHERE supabase_user_id = ?
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Obtener Usuario ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Resultado fetch usuario: {"id":3,"credits":203}
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Datos de usuario obtenidos: Internal ID: 3, Créditos actuales: 203, Nuevos créditos: 1203
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Actualizar Créditos: UPDATE users SET credits = ?, total_credits_purchased = total_credits_purchased + ? WHERE id = ?
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Actualizar Créditos ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Insertar Transacción: INSERT INTO credit_transactions (user_id, transaction_type, credits_amount, credits_before, credits_after, description, stripe_session_id, created_at) VALUES (?, 'purchase', ?, ?, ?, ?, ?, NOW())
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Insertar Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 01:16:46 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando commitear transacción DB.
[28-Jul-2025 01:16:46 Europe/Berlin] ✅ SUCCESS.PHP - Transacción DB commiteada exitosamente.
[28-Jul-2025 01:16:46 Europe/Berlin] ✅ SUCCESS.PHP - Créditos agregados exitosamente al usuario 3.
[28-Jul-2025 01:16:46 Europe/Berlin]    - Créditos antes: 203
[28-Jul-2025 01:16:46 Europe/Berlin]    - Créditos después: 1203
[28-Jul-2025 01:16:46 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado final de addCreditsFromSession: EXITOSO
[28-Jul-2025 01:20:20 Europe/Berlin] 🚀 SUCCESS.PHP - ARCHIVO INICIADO
[28-Jul-2025 01:20:20 Europe/Berlin] 🚀 SUCCESS.PHP - StripeHelper cargado
[28-Jul-2025 01:20:20 Europe/Berlin] 🚀 SUCCESS.PHP - Database config cargado
[28-Jul-2025 01:20:20 Europe/Berlin] ✅ SUCCESS.PHP - Objeto PDO inicializado y conectado.
[28-Jul-2025 01:20:20 Europe/Berlin] ✅ SUCCESS.PHP - Conexión a DB funcional (consulta de prueba exitosa).
[28-Jul-2025 01:20:20 Europe/Berlin] 🚀 SUCCESS.PHP - Variables inicializadas
[28-Jul-2025 01:20:20 Europe/Berlin] 🚀 SUCCESS.PHP - Session ID recibido de GET: cs_test_a1iR43c9R24kgtRp4Ef9aQkmZb88VzLhjgTSLvBzMmlIoru5InJdhtuq7w
[28-Jul-2025 01:20:20 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando procesamiento principal.
[28-Jul-2025 01:20:20 Europe/Berlin] 🔥 SUCCESS.PHP - Session ID presente. Obteniendo sesión de Stripe...
[28-Jul-2025 01:20:22 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado completo de retrieveCheckoutSession: {"success":true,"session":{"id":"cs_test_a1iR43c9R24kgtRp4Ef9aQkmZb88VzLhjgTSLvBzMmlIoru5InJdhtuq7w","object":"checkout.session","adaptive_pricing":{"enabled":true},"after_expiration":null,"allow_promotion_codes":null,"amount_subtotal":500,"amount_total":500,"automatic_tax":{"enabled":false,"liability":null,"provider":null,"status":null},"billing_address_collection":null,"cancel_url":"http:\/\/localhost:8000\/pricing.php?canceled=1","client_reference_id":null,"client_secret":null,"collected_information":null,"consent":null,"consent_collection":null,"created":**********,"currency":"usd","currency_conversion":null,"custom_fields":[],"custom_text":{"after_submit":null,"shipping_address":null,"submit":null,"terms_of_service_acceptance":null},"customer":null,"customer_creation":"if_required","customer_details":{"address":{"city":null,"country":"VE","line1":null,"line2":null,"postal_code":null,"state":null},"email":"<EMAIL>","name":"elsokas","phone":null,"tax_exempt":"none","tax_ids":[]},"customer_email":null,"discounts":[],"expires_at":**********,"invoice":null,"invoice_creation":{"enabled":false,"invoice_data":{"account_tax_ids":null,"custom_fields":null,"description":null,"footer":null,"issuer":null,"metadata":[],"rendering_options":null}},"livemode":false,"locale":null,"metadata":{"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"},"mode":"payment","origin_context":null,"payment_intent":"pi_3RpdrZBMyajljax819fpz5Bv","payment_link":null,"payment_method_collection":"if_required","payment_method_configuration_details":null,"payment_method_options":{"card":{"request_three_d_secure":"automatic"}},"payment_method_types":["card"],"payment_status":"paid","permissions":null,"phone_number_collection":{"enabled":false},"recovered_from":null,"saved_payment_method_options":null,"setup_intent":null,"shipping_address_collection":null,"shipping_cost":null,"shipping_options":[],"status":"complete","submit_type":null,"subscription":null,"success_url":"http:\/\/localhost:8000\/success.php?session_id={CHECKOUT_SESSION_ID}","total_details":{"amount_discount":0,"amount_shipping":0,"amount_tax":0},"ui_mode":"hosted","url":null,"wallet_options":null},"payment_status":"paid","metadata":{"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}}
[28-Jul-2025 01:20:22 Europe/Berlin] 🔥 SUCCESS.PHP - Sesión de Stripe obtenida exitosamente.
[28-Jul-2025 01:20:22 Europe/Berlin] 🔥 SUCCESS.PHP - Payment status de Stripe: paid
[28-Jul-2025 01:20:22 Europe/Berlin] 🔥 SUCCESS.PHP - Metadata de Stripe: {"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 01:20:22 Europe/Berlin] 🔥 SUCCESS.PHP - Condición: payment_status === "paid" CUMPLIDA. Procediendo a agregar créditos...
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Metadata raw: {"credits":"100","type":"credit_purchase","user_id":"aa41cf96-4ef0-4573-bf3f-f43e181bd59f"}
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - User ID extraído: aa41cf96-4ef0-4573-bf3f-f43e181bd59f
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Credits extraídos (int): 100
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Iniciando procesamiento de créditos para User ID: aa41cf96-4ef0-4573-bf3f-f43e181bd59f, Créditos: 100, Sesión: cs_test_a1iR43c9R24kgtRp4Ef9aQkmZb88VzLhjgTSLvBzMmlIoru5InJdhtuq7w
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando iniciar transacción DB.
[28-Jul-2025 01:20:22 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción DB iniciada.
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Check Transacción: SELECT id FROM credit_transactions WHERE stripe_session_id = ? AND user_id = (SELECT id FROM users WHERE supabase_user_id = ?)
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Check Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 01:20:22 Europe/Berlin] ✅ ADD_CREDITS_FROM_SESSION - Transacción NO procesada previamente.
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Obtener Usuario: SELECT id, credits FROM users WHERE supabase_user_id = ?
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Obtener Usuario ejecutado. Resultado: TRUE
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Resultado fetch usuario: {"id":3,"credits":1201}
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Datos de usuario obtenidos: Internal ID: 3, Créditos actuales: 1201, Nuevos créditos: 1301
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Actualizar Créditos: UPDATE users SET credits = ?, total_credits_purchased = total_credits_purchased + ? WHERE id = ?
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Actualizar Créditos ejecutado. Resultado: TRUE
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - SQL Insertar Transacción: INSERT INTO credit_transactions (user_id, transaction_type, credits_amount, credits_before, credits_after, description, stripe_session_id, created_at) VALUES (?, 'purchase', ?, ?, ?, ?, ?, NOW())
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Insertar Transacción ejecutado. Resultado: TRUE
[28-Jul-2025 01:20:22 Europe/Berlin] 🔍 ADD_CREDITS_FROM_SESSION - Intentando commitear transacción DB.
[28-Jul-2025 01:20:22 Europe/Berlin] ✅ SUCCESS.PHP - Transacción DB commiteada exitosamente.
[28-Jul-2025 01:20:22 Europe/Berlin] ✅ SUCCESS.PHP - Créditos agregados exitosamente al usuario 3.
[28-Jul-2025 01:20:22 Europe/Berlin]    - Créditos antes: 1201
[28-Jul-2025 01:20:22 Europe/Berlin]    - Créditos después: 1301
[28-Jul-2025 01:20:22 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado final de addCreditsFromSession: EXITOSO
[28-Jul-2025 02:58:31 Europe/Berlin] 🔥 SUCCESS.PHP - Iniciando con session_id: cs_test_a1cZo6sg3V3stKht4Rq7n8MQKKCXylhzJunKpbXBpl6nrfznecLDorRw7u
[28-Jul-2025 02:58:32 Europe/Berlin] 🔥 SUCCESS.PHP - Sesión obtenida: paid
[28-Jul-2025 02:58:32 Europe/Berlin] 🔥 SUCCESS.PHP - Pago confirmado, enviando a procesamiento interno...
[28-Jul-2025 02:58:42 Europe/Berlin] ❌ PROCESS_CREDITS - Error cURL: Operation timed out after 10005 milliseconds with 0 bytes received
[28-Jul-2025 02:58:42 Europe/Berlin] 🔥 SUCCESS.PHP - Resultado procesamiento: FALLÓ
