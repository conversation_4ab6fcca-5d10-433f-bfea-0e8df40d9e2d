# Protección para endpoints internos de la API

# Denegar acceso directo al endpoint de procesamiento de pagos
<Files "procesar_pago_stripe.php">
    # Solo permitir acceso desde localhost
    Require ip 127.0.0.1
    Require ip ::1
    
    # Denegar todo lo demás
    Require all denied
    
    # Headers de seguridad adicionales
    Header always set X-Robots-Tag "noindex, nofollow"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "DENY"
</Files>

# Opcional: Ocultar archivos .env si están en esta carpeta
<Files ".env">
    Require all denied
</Files>

# Opcional: Ocultar archivos de configuración
<Files "*.config.php">
    Require all denied
</Files>
