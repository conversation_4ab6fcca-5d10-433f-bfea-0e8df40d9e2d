<?php
/**
 * PÁGINA DE ÉXITO - NUEVA ARQUITECTURA SEGURA
 */

ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/debug_stripe_success.log');


require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/utils/stripe_helper.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$sessionId = $_GET['session_id'] ?? null;
$sessionInfo = null;
$error = null;
$creditsProcessed = false;

error_log('🔥 SUCCESS.PHP - Iniciando con session_id: ' . ($sessionId ?? 'NULL'));

if ($sessionId) {
    $result = StripeHelper::retrieveCheckoutSession($sessionId);
    
    if ($result['success']) {
        $sessionInfo = $result['session'];
        error_log('🔥 SUCCESS.PHP - Sesión obtenida: ' . $sessionInfo->payment_status);
        
        if ($sessionInfo->payment_status === 'paid') {
            error_log('🔥 SUCCESS.PHP - Pago confirmado, enviando a procesamiento interno...');
            $creditsProcessed = processCreditsSecurely($sessionId);
            error_log('🔥 SUCCESS.PHP - Resultado procesamiento: ' . ($creditsProcessed ? 'ENVIADO' : 'FALLÓ'));
        }
    } else {
        $error = $result['error'];
    }
} else {
    $error = 'ID de sesión no proporcionado';
}

function processCreditsSecurely($sessionId) {
    try {
        $internalToken = $_ENV['INTERNAL_PROCESSING_TOKEN'] ?? '';
        if (empty($internalToken)) {
            error_log('❌ PROCESS_CREDITS - Token interno no configurado');
            return false;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => 'http://localhost:8000/api/procesar_pago_stripe.php',
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query([
                'session_id' => $sessionId,
                'internal_token' => $internalToken
            ]),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'AccelPrompt-Internal-Processor/1.0'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        if ($curlError) {
            error_log('❌ PROCESS_CREDITS - Error cURL: ' . $curlError);
            return false;
        }
        
        if ($httpCode !== 200) {
            error_log('❌ PROCESS_CREDITS - HTTP Error: ' . $httpCode);
            return false;
        }
        
        $result = json_decode($response, true);

        // DEBUG: Ver exactamente qué devuelve el endpoint
        error_log('🔍 PROCESS_CREDITS - Response raw: ' . $response);
        error_log('🔍 PROCESS_CREDITS - Result decoded: ' . json_encode($result));

        if ($result && isset($result['success']) && $result['success']) {
            //error_log('✅ PROCESS_CREDITS - Créditos procesados exitosamente');
            return true;
        } else {
            error_log('❌ PROCESS_CREDITS - Error: ' . ($result['error'] ?? 'Unknown'));
            error_log('❌ PROCESS_CREDITS - Result structure: ' . json_encode(array_keys($result ?? [])));
            return false;
        }
        
    } catch (Exception $e) {
        error_log('❌ PROCESS_CREDITS - Exception: ' . $e->getMessage());
        return false;
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pago Exitoso - AccelPrompt</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 dark:bg-gray-900">
    <div class="min-h-screen flex items-center justify-center py-12 px-4">
        <div class="max-w-md w-full space-y-8">
            <?php if ($sessionInfo && $sessionInfo->payment_status === 'paid'): ?>
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">¡Pago Exitoso!</h2>
                </div>

                <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Detalles de la compra</h3>
                    
                    <dl class="space-y-3">
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-gray-500">Créditos comprados:</dt>
                            <dd class="text-sm text-gray-900 dark:text-white font-semibold">
                                <?php echo htmlspecialchars($sessionInfo->metadata->credits ?? '0'); ?>
                            </dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-gray-500">Monto pagado:</dt>
                            <dd class="text-sm text-gray-900 dark:text-white font-semibold">
                                $<?php echo number_format($sessionInfo->amount_total / 100, 2); ?> USD
                            </dd>
                        </div>
                    </dl>

                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <?php if ($creditsProcessed): ?>
                            <p class="text-sm text-green-600 font-medium">
                                ✅ Tus créditos han sido procesados correctamente.
                            </p>
                        <?php else: ?>
                            <p class="text-sm text-yellow-600">
                                ⚠️ Los créditos se están procesando. Aparecerán en tu cuenta en unos momentos.
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <button onclick="window.location.href='/'" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                    Continuar a usar créditos
                </button>

            <?php else: ?>
                <div class="text-center">
                    <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">Error en el Pago</h2>
                    <p class="mt-2 text-sm text-gray-600"><?php echo htmlspecialchars($error ?? 'Error desconocido'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        <?php if ($sessionInfo && $sessionInfo->payment_status === 'paid'): ?>
        localStorage.setItem('credits_purchase_completed', 'true');
        localStorage.setItem('credits_purchased', '<?php echo htmlspecialchars($sessionInfo->metadata->credits ?? '0'); ?>');
        
        setTimeout(function() {
            window.location.href = '/';
        }, 10000);
        <?php endif; ?>
    </script>
</body>
</html>
