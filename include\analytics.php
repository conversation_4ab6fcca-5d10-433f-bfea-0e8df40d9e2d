<!-- <?php require('config.php'); ?>


<script>
  ! function(t, e) {
    var o, n, p, r;
    e.__SV || (window.posthog = e, e._i = [], e.init = function(i, s, a) {
        function g(index, arg) {
          var c = e._i[index];
          return c && (c.push(arg), e);
        }
        t.console && void 0 !== i.debug && t.console.log('PostHog:', i);
        var v, y, d, l = e;
        if (void 0 !== a) l = e.init(s, a).get();
        else if (void 0 !== s) l = e.init(s, [i]).get();
        else return l;
        (v = l.invoked) ? (t.console && t.console.info('PostHog: already invoked', i), !1) : (v = !0, l.load = function(i) {
            this.i = i;
            this.invoked = !0;
            this._j.push(["identify", this.i]);
            this._j.push(["_trackPageview"]);
            this._j.push(["trackQueued"]);
          }, l.identify = function(i, s) {
            this.i = i;
            this._j.push(["identify", i, s])
          }, l.alias = function(i, s) {
            this._j.push(["alias", i, s])
          }, l.onFeatureFlags = function(i) {
            this._j.push(["onFeatureFlags", i])
          }, l.getFeatureFlag = function(i) {
            return this.a[i]
          }, l.getFeatureFlagPayload = function(i) {
            return this.a[i]?._payload
          }, l.getFeatureFlagWithPayload = function(i) {
            return this.getFeatureFlag(i) ? {
              value: this.getFeatureFlag(i),
              payload: this.getFeatureFlagPayload(i)
            } : undefined
          }, l.group = function(i, s, a) {
            this._j.push(["group", i, s, a])
          }, l.withGroups = function(i) {
            return g(this._i.push([
              []
            ]) - 1, i)
          }, l.track = function(i, s, a) {
            this._j.push(["track", i, s, a])
          }, l.trackPageView = function() {
            this._j.push(["_trackPageview"])
          }, l.register = function(i, s) {
            this._j.push(["register", i, s])
          }, l.registerOnce = function(i, s) {
            this._j.push(["registerOnce", i, s])
          }, l.unregister = function(i) {
            this._j.push(["unregister", i])
          }, l.clearAllEventListeners = function() {
            this._j.push(["clearAllEventListeners"])
          }, l.setPersonProperties = function(i) {
            this._j.push(["setPersonProperties", i])
          }, l.setGroupProperties = function(i, s) {
            this._j.push(["setGroupProperties", i, s])
          }, l._w = function(i) {
            this._j.push(["_w", i])
          }, l._q = [], v = l._j = [], (d = t.createElement("script")).async = !0, d.src <?php echo $posthog_region; ?>.posthog.com/phc.js?apiKey=<?php echo $posthog_api_key; ?>" + (s ? "&config=" + encodeURIComponent(JSON.stringify(s)) : ""), (y = t.getElementsByTagName("script")[0]).parentNode.insertBefore(d, y), l.register(a, {
            distinct_id: i
          }), l._i.push([c, s, a])
        }, e.__SV = 1)
    }(document, window.posthog || []);
    posthog.init('<?php echo $posthog_api_key; ?>', {
      api_host: '<?php echo $posthog_host; ?>'
    });
</script> -->