/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./*.php",
    "./include/*.php",
    "./js/*.js"
  ],
  darkMode: 'class', // Usamos 'class' para activar el modo oscuro con clases
  theme: {
    extend: {
      colors: {
        // Colores personalizados para el modo claro
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49',
        },
        // Colores para el modo oscuro
        dark: {
          bg: '#111827',
          surface: '#1f2937',
          border: '#374151',
          text: {
            primary: '#f3f4f6',
            secondary: '#d1d5db',
            muted: '#9ca3af'
          }
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'dark-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.4)',
      }
    },
  },
  plugins: [],
}
