-- Tabla para almacenar el historial de prompts generados por los usuarios
CREATE TABLE IF NOT EXISTS prompt_history (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    tool_type VARCHAR(50) NOT NULL, -- 'coding' o 'writing'
    tool_name VARCHAR(100) NOT NULL, -- nombre de la herramienta específica
    prompt_title VARCHAR(255),
    prompt_content TEXT NOT NULL,
    form_data JSONB, -- datos del formulario utilizados para generar el prompt
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS idx_prompt_history_user_id ON prompt_history(user_id);
CREATE INDEX IF NOT EXISTS idx_prompt_history_created_at ON prompt_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_prompt_history_tool_type ON prompt_history(tool_type);

-- Función para actualizar el campo updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para actualizar updated_at automáticamente
CREATE TRIGGER update_prompt_history_updated_at 
    BEFORE UPDATE ON prompt_history 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Política de seguridad RLS (Row Level Security)
ALTER TABLE prompt_history ENABLE ROW LEVEL SECURITY;

-- Los usuarios solo pueden ver y modificar su propio historial
CREATE POLICY "Users can view their own history" ON prompt_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own history" ON prompt_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own history" ON prompt_history
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own history" ON prompt_history
    FOR DELETE USING (auth.uid() = user_id);
