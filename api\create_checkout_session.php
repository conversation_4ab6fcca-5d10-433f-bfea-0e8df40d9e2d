<?php
require_once __DIR__ . '/../utils/stripe_helper.php';
require_once __DIR__ . '/../utils/jwt_helper.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Si es una solicitud OPTIONS (preflight), terminar aquí
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Verificar que sea una solicitud POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

// Validar autenticación JWT (requerida)
$user = JWTHelper::requireAuth();
$userId = $user['user_id'];

// Obtener el contenido JSON de la solicitud
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['credits']) || !isset($input['amount'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Créditos y monto son requeridos']);
    exit;
}

$credits = (int)$input['credits'];
$amount = (float)$input['amount'];

// Validar que los valores sean positivos
if ($credits <= 0 || $amount <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Créditos y monto deben ser positivos']);
    exit;
}

// Validar combinaciones válidas de créditos/precio (opcional - puedes personalizar)
$validPackages = [
    100 => 5.00,   // 100 créditos = $5
    250 => 10.00,  // 250 créditos = $10
    500 => 20.00,  // 500 créditos = $20
    1000 => 35.00, // 1000 créditos = $35
];

if (!isset($validPackages[$credits]) || $validPackages[$credits] !== $amount) {
    http_response_code(400);
    echo json_encode(['error' => 'Paquete de créditos no válido']);
    exit;
}

try {
    // URLs de éxito y cancelación
    $baseUrl = $_ENV['SITE_URL'] ?? 'http://localhost:8000';
    $successUrl = $baseUrl . '/success.php?session_id={CHECKOUT_SESSION_ID}';
    $cancelUrl = $baseUrl . '/pricing.php?canceled=1';
    
    // Crear sesión de checkout
    $result = StripeHelper::createCheckoutSession(
        $credits,
        $amount,
        $userId,
        $successUrl,
        $cancelUrl
    );
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'session_id' => $result['session_id'],
            'checkout_url' => $result['checkout_url']
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => $result['error']
        ]);
    }
    
} catch (Exception $e) {
    error_log('Error creating checkout session: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error interno del servidor'
    ]);
}
