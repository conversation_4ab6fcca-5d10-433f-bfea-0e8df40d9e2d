<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/jwt_helper.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido']);
    exit;
}

try {
    // Usar el mismo método que generate_prompt_g.php
    $user = JWTHelper::requireAuth();
    $supabaseUserId = $user['user_id'];

    error_log('🔍 DEBUG check_signup_bonus - Usuario autenticado: ' . json_encode($user));
    error_log('🔍 DEBUG check_signup_bonus - User ID: ' . $supabaseUserId);
    
    // Verificar si el usuario existe en nuestra DB
    $userStmt = $pdo->prepare("SELECT id, credits FROM users WHERE supabase_user_id = ?");
    $userStmt->execute([$supabaseUserId]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(404);
        echo json_encode(['error' => 'Usuario no encontrado']);
        exit;
    }
    
    $internalUserId = $user['id'];
    $currentCredits = $user['credits'];
    
    // Verificar si ya recibió el bonus de registro (SIMPLIFICADO)
    // Si el usuario tiene exactamente 0 créditos, es nuevo y necesita bonus
    // Si tiene > 0, ya recibió bonus o compró créditos
    $alreadyHasBonus = $currentCredits > 0;
    
    if ($alreadyHasBonus) {
        // Ya recibió el bonus, solo devolver créditos actuales
        echo json_encode([
            'success' => true,
            'credits' => $currentCredits,
            'bonus_given' => false,
            'message' => 'Usuario ya tiene créditos de bienvenida'
        ]);
        exit;
    }
    
    // Es primer registro, dar 10 créditos gratuitos
    $pdo->beginTransaction();
    
    try {
        // Agregar 10 créditos DIRECTAMENTE a users.credits
        $newCredits = 10; // Siempre 10 para nuevos usuarios
        $updateUserStmt = $pdo->prepare("UPDATE users SET credits = 10 WHERE id = ?");
        $updateUserStmt->execute([$internalUserId]);

        // Registrar la transacción de bonus SOLO para historial
        $transactionStmt = $pdo->prepare("
            INSERT INTO credit_transactions
            (user_id, transaction_type, credits_amount, credits_before, credits_after, description, created_at)
            VALUES (?, 'signup_bonus', 10, 0, 10, 'Créditos de bienvenida - Registro gratuito', NOW())
        ");
        $transactionStmt->execute([$internalUserId]);

        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'credits' => $newCredits,
            'bonus_given' => true,
            'message' => '¡Bienvenido! Has recibido 10 créditos gratuitos'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log('Error in check_signup_bonus.php: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'error' => 'Error interno del servidor',
        'details' => $e->getMessage()
    ]);
}
?>
